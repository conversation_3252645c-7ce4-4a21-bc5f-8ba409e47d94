# pass1_processing.py v3.20
import re
import traceback
from typing import TYPE_CHECKING, Dict, Any, Optional

if TYPE_CHECKING:
    from crass import Assembler
    from symbol_table import SymbolTable
    from instruction_table import InstructionTable
    from assembler_state import AssemblerState 
    
from errors import AsmEx<PERSON>, ErrorReporter 
from expression import ExpressionError, evaluate_expression, evaluate_data_item, substitute_micros
from operand_parser import OperandParseError
import pseudo_op_handlers 
from assembler_state import AssemblerState as AssemblerStateClass, handle_force_upper as global_handle_force_upper
from assembler_state import FORCE_NEW_WORD_MNEMONICS_PASS1, DEFERRED_FORCE_MNEMONICS_PASS1, MAX_BITS_IN_WORD # Corrected import
from lexer import parse_line
import pass_logic as pl 
from conditional_processing import evaluate_condition
# MAX_BITS_IN_WORD is now imported from assembler_state


def process_line_pass_1(
    state: 'AssemblerStateClass', 
    symbol_table: 'SymbolTable',
    instruction_table: 'InstructionTable',
    error_reporter: 'ErrorReporter', 
    macro_definitions: Dict[str, Any],
    micro_definitions: Dict[str, str],
    assembler: 'Assembler',
    line_num: int,
    parsed: Dict[str, Any]
) -> bool: 
    debug_mode = state.debug_mode
    state.current_line_number = line_num 

    original_line_text = parsed['original']
    label = parsed['label']
    mnemonic = parsed['opcode']
    operand_str = parsed.get('operand_str') 
    if operand_str is None: 
        operand_str = ""

    if debug_mode:
        print(f"DEBUG P1 ENTER L{line_num}: Parsed: label='{label}', opcode='{mnemonic}', operand='{operand_str}'")
        print(f"    P1 L{line_num} START: state.block_lcs (id={id(state.block_lcs)}) = {{ {', '.join([f'{k}: {v:o}' for k, v in state.block_lcs.items()])} }}")
        print(f"    State Before: LC={state.location_counter:o}, PC={state.position_counter}, Block='{state.current_block}', LOC_Abs={state.lc_is_absolute_due_to_loc}, DefPend={state.deferred_force_upper_pending}, PreLOCBlk={state.pre_loc_block_name}")
        print(f"    DEBUG P1 L{line_num}: state.is_defining = '{state.is_defining}', state.current_remote_block_name = '{state.current_remote_block_name}'")

    if state.current_remote_block_name and not parsed.get('is_remote_expansion'): 
        mnemonic_in_remote = parsed['opcode'].upper() if parsed['opcode'] else None
        operand_field_for_rmt_check = parsed.get('operand_str', "")
        if mnemonic_in_remote == "RMT" and not (operand_field_for_rmt_check or "").strip():
            if debug_mode: print(f"DEBUG P1 RMT: L{line_num} Ending remote block '{state.current_remote_block_name}'")
            state.current_remote_block_name = None
        elif mnemonic_in_remote == "END": 
            if debug_mode: print(f"DEBUG P1 RMT: L{line_num} END encountered, ending remote block '{state.current_remote_block_name}'")
            state.current_remote_block_name = None
        else:
            if state.current_remote_block_name not in assembler.remote_blocks:
                 assembler.remote_blocks[state.current_remote_block_name] = {'lines': [], 'start_line': state.current_line_number -1} 
            assembler.remote_blocks[state.current_remote_block_name]['lines'].append(original_line_text)
        return True 

    if state.is_defining:
        mnemonic_in_def = parsed['opcode'].upper() if parsed['opcode'] else None
        if mnemonic_in_def == "ENDM":
            if state.current_definition_name:
                 if state.is_defining in ("MACRO", "OPDEF"):
                      def_fmt = getattr(state, '_temp_def_format', 1) 
                      macro_definitions[state.current_definition_name] = {
                           'type': state.is_defining, 'params': state.current_definition_params,
                           'body': state.current_definition_lines, 
                           'defined_line': line_num, 
                           'definition_format': def_fmt,
                           'source_line_numbers': getattr(state, '_temp_def_source_lines', [])
                      }
                      if hasattr(state, '_temp_def_format'): delattr(state, '_temp_def_format')
                      if hasattr(state, '_temp_def_source_lines'): delattr(state, '_temp_def_source_lines')
                      if debug_mode: print(f"DEBUG P1 MACRO/OPDEF: Defined '{state.current_definition_name}' (Fmt: {def_fmt}) with {len(state.current_definition_lines)} lines.")
            else: error_reporter.add_error(f"{mnemonic_in_def} encountered outside of a named definition block", line_num, code='S')
            state.is_defining = None; state.current_definition_name = None
            state.current_definition_params = []; state.current_definition_lines = []
        else:
            if state.is_defining in ("MACRO", "OPDEF"):
                state.current_definition_lines.append(original_line_text)
                if not hasattr(state, '_temp_def_source_lines'): state._temp_def_source_lines = []
                state._temp_def_source_lines.append(line_num)
        return True 

    is_comment_only_line = parsed['is_comment_line']
    is_blank_line = not label and not mnemonic and not operand_str.strip()
    if is_comment_only_line or is_blank_line:
        if label and is_comment_only_line and label != '-': 
            if state.position_counter != 0:
                error_reporter.add_warning(f"Label '{label}' on comment line L{line_num} is not word-aligned (PC={state.position_counter}). Value may be unexpected.", line_num, 'A')
            block_for_label = state.pre_loc_block_name if state.lc_is_absolute_due_to_loc and state.pre_loc_block_name else state.current_block
            sym_type = 'absolute' if state.lc_is_absolute_due_to_loc or block_for_label == '*ABS*' else 'relocatable'
            value_for_label = state.location_counter 
            attrs_comment_label = {'type': sym_type, 'redefinable': False, 'block': block_for_label}
            if not symbol_table.define(label, value_for_label, line_num, attrs_comment_label, state.current_qualifier):
                pass 
        return True

    mnemonic_upper = mnemonic.upper() if mnemonic else "" 
    
    if mnemonic_upper.startswith("IF") or mnemonic_upper == "ELSE" or mnemonic_upper == "ENDIF":
        currently_active_outer = not state.conditional_stack or state.conditional_stack[-1]
        eval_result = False 
        if mnemonic_upper.startswith("IF"):
            if currently_active_outer: 
                try:
                    eval_result = evaluate_condition(assembler, line_num, mnemonic_upper, operand_str) 
                except (ExpressionError, AsmException) as e:
                    eval_result = False 
            state.conditional_stack.append(currently_active_outer and eval_result)
        elif mnemonic_upper == "ELSE":
            if not state.conditional_stack: error_reporter.add_error("ELSE without matching IF", line_num, 'S')
            else:
                outer_active = state.conditional_stack[-2] if len(state.conditional_stack) > 1 else True
                if_branch_was_true = state.conditional_stack.pop()
                state.conditional_stack.append(outer_active and not if_branch_was_true)
        elif mnemonic_upper == "ENDIF":
            if not state.conditional_stack: error_reporter.add_error("ENDIF without matching IF", line_num, 'S')
            else: state.conditional_stack.pop()
        if not state.conditional_stack: state.conditional_stack.append(True)
        return True 

    if not state.conditional_stack[-1]:
        if debug_mode: print(f"DEBUG P1 L{line_num}: Line '{original_line_text.strip()}' SKIPPED due to false conditional.")
        if label and not symbol_table.is_defined(label, state.current_qualifier):
            lc_for_skipped_label = state.location_counter
            pc_for_skipped_label = state.position_counter
            if pc_for_skipped_label != 0:
                 error_reporter.add_warning(f"Label '{label}' on skipped line L{line_num} is not word-aligned (PC={pc_for_skipped_label}).", line_num, 'A')
            block_ctx = state.pre_loc_block_name if state.lc_is_absolute_due_to_loc and state.pre_loc_block_name else state.current_block
            sym_type = 'absolute' if state.lc_is_absolute_due_to_loc or block_ctx == '*ABS*' else 'relocatable'
            attrs_skip = {'type': sym_type, 'redefinable': False, 'block': block_ctx}
            symbol_table.define(label, lc_for_skipped_label, line_num, attrs_skip, state.current_qualifier)
        return True

    if state.skip_count > 0:
        if mnemonic_upper != "END" and mnemonic_upper != "ENDL": 
            state.skip_count -= 1
            if debug_mode: print(f"DEBUG P1 L{line_num}: Line '{original_line_text.strip()}' SKIPPED due to active SKIP. skip_count now {state.skip_count}")
            return True

    lc_at_line_start = state.location_counter
    pc_at_line_start = state.position_counter
    
    if state.deferred_force_upper_pending:
        if label == '-': 
            state.deferred_force_upper_pending = False
            if debug_mode: print(f">>> DEBUG LC P1 L{line_num}: Deferred force canceled by '-' label.")
        elif not (mnemonic_upper == "EQU" and operand_str.strip() == '*'): 
            if debug_mode: print(f">>> DEBUG LC P1 L{line_num}: Executing PREVIOUS deferred force. LC was {lc_at_line_start:o}, PC was {pc_at_line_start}.")
            state.location_counter = lc_at_line_start
            state.position_counter = pc_at_line_start
            global_handle_force_upper(state, None, error_reporter, line_num) 
            lc_at_line_start = state.location_counter
            pc_at_line_start = state.position_counter
        elif debug_mode:
            print(f">>> DEBUG LC P1 L{line_num}: Deferred force pending, but current is EQU *. Symbol will use pre-force LC.")

    if mnemonic_upper in FORCE_NEW_WORD_MNEMONICS_PASS1:
        if pc_at_line_start != 0:
            if debug_mode: print(f">>> DEBUG LC P1 L{line_num}: Mnemonic '{mnemonic_upper}' forces new word. PC was {pc_at_line_start}.")
            state.location_counter = lc_at_line_start; state.position_counter = pc_at_line_start
            global_handle_force_upper(state, None, error_reporter, line_num)
            lc_at_line_start = state.location_counter; pc_at_line_start = state.position_counter
    elif label and label not in ['+', '-'] and not (mnemonic_upper == "EQU" or mnemonic_upper == "SET"):
        if pc_at_line_start != 0:
            if debug_mode: print(f">>> DEBUG LC P1 L{line_num}: Label '{label}' requires word alignment. PC was {pc_at_line_start}.")
            state.location_counter = lc_at_line_start; state.position_counter = pc_at_line_start
            global_handle_force_upper(state, None, error_reporter, line_num)
            lc_at_line_start = state.location_counter; pc_at_line_start = state.position_counter
    elif label == '+': 
        if pc_at_line_start != 0:
            if debug_mode: print(f">>> DEBUG LC P1 L{line_num}: '+' label requires word alignment. PC was {pc_at_line_start}.")
            state.location_counter = lc_at_line_start; state.position_counter = pc_at_line_start
            global_handle_force_upper(state, None, error_reporter, line_num)
            lc_at_line_start = state.location_counter; pc_at_line_start = state.position_counter

    label_defined_by_pseudo = mnemonic_upper in {
        "EQU", "=", "SET", "IDENT", "MACRO", "OPDEF", "MICRO", "LOC", "RMT", "HERE", "COMMON", "END" 
    }
    if label and not label_defined_by_pseudo and label != '-':
        block_context = state.pre_loc_block_name if state.lc_is_absolute_due_to_loc and state.pre_loc_block_name else state.current_block
        sym_type = 'absolute' if state.lc_is_absolute_due_to_loc or block_context == '*ABS*' else 'relocatable'
        label_value = lc_at_line_start
        if pc_at_line_start != 0: 
             error_reporter.add_warning(f"Label '{label}' on L{line_num} defined at non-word boundary (PC={pc_at_line_start}) after alignment attempts. Value is LC of current word.", line_num, 'A')
        attrs = {'type': sym_type, 'redefinable': False, 'block': block_context}
        if mnemonic_upper == "EQU" and operand_str.strip() == '*': attrs['equ_star'] = True
        if debug_mode:
            print(f">>> DEBUG LC P1 LabelDef (Regular): Defining '{label}' Value={label_value:o} (Type:{sym_type}, Block:{block_context}, PC={pc_at_line_start})")
        if not symbol_table.define(label, label_value, line_num, attrs, state.current_qualifier):
            pass 

    state.location_counter = lc_at_line_start
    state.position_counter = pc_at_line_start

    is_macro_call = False
    is_opdef_call = False
    actual_def_name_for_call = None 

    if mnemonic_upper: 
        if mnemonic_upper in macro_definitions and macro_definitions[mnemonic_upper].get('type') == "MACRO":
            is_macro_call = True
            actual_def_name_for_call = mnemonic_upper
        elif mnemonic_upper + "Q" in macro_definitions and macro_definitions[mnemonic_upper + "Q"].get('type') == "OPDEF":
            is_opdef_call = True
            actual_def_name_for_call = mnemonic_upper + "Q"
        elif mnemonic_upper in macro_definitions and macro_definitions[mnemonic_upper].get('type') == "OPDEF":
            is_opdef_call = True
            actual_def_name_for_call = mnemonic_upper

    if instruction_table.is_pseudo_op(mnemonic_upper):
        if not pseudo_op_handlers.handle_pseudo_op_pass_1(assembler, line_num, mnemonic_upper, operand_str, label, label_defined_by_pseudo, lc_at_line_start, pc_at_line_start):
            return False 
    elif instruction_table.is_instruction(mnemonic_upper):
        instr_details = instruction_table.get_instruction_details(mnemonic_upper)
        if instr_details:
            operand_str_for_sizing = substitute_micros(operand_str, assembler, line_num)
            estimated_bits = pl._estimate_instruction_width_pass1(assembler, line_num, mnemonic_upper, instr_details, operand_str_for_sizing)
            current_pc_for_instr = state.position_counter
            if estimated_bits == 30:
                if current_pc_for_instr not in (0, 15, 30): 
                    if current_pc_for_instr == 45: 
                        global_handle_force_upper(state, None, error_reporter, line_num)
                    else: 
                        global_handle_force_upper(state, None, error_reporter, line_num)
            elif estimated_bits == 60:
                if current_pc_for_instr != 0:
                    global_handle_force_upper(state, None, error_reporter, line_num)
            elif estimated_bits == 15: 
                if current_pc_for_instr + 15 > MAX_BITS_IN_WORD: 
                    global_handle_force_upper(state, None, error_reporter, line_num)
            state.advance_lc(estimated_bits)
            parsed['pass1_width_estimate'] = estimated_bits 
        else: 
            error_reporter.add_error(f"Internal error: Instruction details missing for '{mnemonic_upper}'", line_num, 'F')
    elif is_macro_call or is_opdef_call:
        if actual_def_name_for_call is None: 
            error_reporter.add_error(f"Internal: Macro/OPDEF call detected for '{mnemonic_upper}' but definition name not resolved.", line_num, 'F')
            return False
            
        macro_def = macro_definitions[actual_def_name_for_call]
        if debug_mode: print(f"DEBUG P1 L{line_num}: Sizing {macro_def['type']} call '{mnemonic_upper}' (using def: '{actual_def_name_for_call}')")
        
        temp_sizing_state = AssemblerStateClass(error_reporter=ErrorReporter(), symbol_table=symbol_table, debug_mode=debug_mode, assembler_ref=assembler)
        temp_sizing_state.current_base = state.current_base
        temp_sizing_state.current_code = state.current_code
        temp_sizing_state.pass_number = 1
        temp_sizing_state.current_qualifier = state.current_qualifier
        temp_sizing_state.location_counter = 0
        temp_sizing_state.position_counter = 0
        
        temp_assembler_for_sizing = type(assembler)("", None, None, debug_mode)
        temp_assembler_for_sizing.state = temp_sizing_state
        temp_assembler_for_sizing.symbol_table = symbol_table
        temp_assembler_for_sizing.instruction_table = instruction_table
        temp_assembler_for_sizing.error_reporter = temp_sizing_state.error_reporter 
        temp_assembler_for_sizing.macro_definitions = macro_definitions 
        temp_assembler_for_sizing.micro_definitions = micro_definitions 
        temp_assembler_for_sizing.remote_blocks = assembler.remote_blocks 
        for body_line_str in macro_def.get('body', []): 
            parsed_body_line = parse_line(body_line_str, 0) 
            parsed_body_line['original'] = body_line_str
            parsed_body_line['is_macro_expansion'] = True 
            process_line_pass_1(temp_sizing_state, symbol_table, instruction_table,
                                temp_sizing_state.error_reporter,
                                macro_definitions, micro_definitions,
                                temp_assembler_for_sizing, 
                                0, parsed_body_line)
        total_bits_for_macro = temp_sizing_state.location_counter * MAX_BITS_IN_WORD + temp_sizing_state.position_counter
        if debug_mode: print(f"DEBUG P1 L{line_num}: Estimated size for {macro_def['type']} '{mnemonic_upper}' = {total_bits_for_macro} bits.")
        if total_bits_for_macro > 0 and state.position_counter != 0:
            if debug_mode: print(f"DEBUG P1 L{line_num}: Aligning main LC for {macro_def['type']} '{mnemonic_upper}' from PC={state.position_counter} before advancing {total_bits_for_macro} bits.")
            global_handle_force_upper(state, None, error_reporter, line_num, increment_block_size_on_force=False) 
        state.advance_lc(total_bits_for_macro)
        parsed['pass1_width_estimate'] = total_bits_for_macro
    else: 
        if mnemonic_upper and not error_reporter.has_error_on_line(line_num): 
             error_reporter.add_error(f"Unknown Mnemonic: {mnemonic_upper}", line_num, code='U')

    if mnemonic_upper in DEFERRED_FORCE_MNEMONICS_PASS1:
        if state.position_counter != 0:
            state.deferred_force_upper_pending = True
            if debug_mode: print(f">>> DEBUG LC P1 L{line_num}: Mnemonic {mnemonic_upper} SET deferred_force_upper_pending because PC={state.position_counter}.")
    elif state.position_counter == 0 : 
        state.deferred_force_upper_pending = False

    if state.debug_mode:
        print(f"    State After : LC={state.location_counter:o}, PC={state.position_counter}, Block='{state.current_block}', DefPend={state.deferred_force_upper_pending}, PreLOCBlk={state.pre_loc_block_name}")
        if state.current_block != '*ABS*':
            print(f"    Block '{state.current_block}' size: {state.block_lcs.get(state.current_block, 0):o}")
        if state.pre_loc_block_name and state.pre_loc_block_name != state.current_block:
            print(f"    PreLOC Block '{state.pre_loc_block_name}' size: {state.block_lcs.get(state.pre_loc_block_name, 0):o}")
        print(f"    P1 L{line_num} END  : state.block_lcs (id={id(state.block_lcs)}) = {{ {', '.join([f'{k}: {v:o}' for k, v in state.block_lcs.items()])} }}")

    return not state.end_statement_processed
# pass1_processing.py v3.20
