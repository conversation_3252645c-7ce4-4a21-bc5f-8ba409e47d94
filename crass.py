# crass.py v1.96
"""
CRASS - COMPASS Cross-Assembler for CDC 6000 series.
Main application entry point.

v1.82: Add more id(symbol_table) checks for debugging EQU *.
v1.83: Add macro_definitions dictionary to Assembler class.
v1.84: Add end_statement_label attribute for deferred END label definition.
v1.85: Add block_base_addresses attribute for Pass 2 block LC calculation.
...
v1.94: Link assembler_ref to OutputGenerator for debug_mode access.
v1.95: Remove redundant debug_mode kwarg from OutputGenerator instantiation.
v1.96: Correct call to ErrorReporter.print_summary by removing file_handle kwarg.
"""

import argparse
import sys
import os
import traceback
from typing import List, Optional, Tuple, Dict, Any

from lexer import parse_line
from symbol_table import SymbolTable
from instruction_table import InstructionTable
from assembler_state import AssemblerState
from output_generator import OutputGenerator
from errors import ErrorReporter, AsmException, AsmWarning
from pass_logic import perform_pass

DEFAULT_BINARY_FILENAME = "binfile"
VERSION = "0.0.96"


class Assembler:
    """ Encapsulates the assembler state and processes. """
    def __init__(self, input_filename: str, listing_filename: Optional[str] = None, binary_filename: Optional[str] = None, debug_mode: bool = False):
        self.input_filename = input_filename
        self.listing_filename = listing_filename
        self.binary_filename = binary_filename if binary_filename else DEFAULT_BINARY_FILENAME
        self.debug_mode = debug_mode
        self.error_reporter = ErrorReporter()
        self.symbol_table = SymbolTable(self.error_reporter, debug_mode=self.debug_mode)
        self.instruction_table = InstructionTable()
        self.state = AssemblerState(error_reporter=self.error_reporter, symbol_table=self.symbol_table, debug_mode=self.debug_mode, assembler_ref=self)
        
        self.output_generator: Optional[OutputGenerator] = None
        self.lines: List[str] = []
        self.parsed_lines: Dict[int, Dict[str, Any]] = {}
        self._listing_handle = None
        self._binary_handle = None
        
        self.macro_definitions: Dict[str, Dict[str, Any]] = {}
        self.micro_definitions: Dict[str, str] = {}
        self.remote_blocks: Dict[str, Dict[str, Any]] = {}
        
        self.block_base_addresses: Dict[str, int] = {}
        self.total_program_length: int = 0
        self.endl_listing_value: Optional[int] = None

        self.end_statement_label: Optional[str] = None
        self.ident_line_num: Optional[int] = None

        if self.debug_mode:
            print(f"Debug CRASS Init: Symbol table ID = {id(self.symbol_table)}")
            print(f"Debug CRASS Init: AssemblerState ID = {id(self.state)}")


    def assemble(self):
        print(f"Starting assembly for: {self.input_filename}")
        if not self._read_input_file():
            self._print_summary()
            return False

        self._listing_handle = sys.stdout
        opened_listing_file = False
        if self.listing_filename:
            try:
                self._listing_handle = open(self.listing_filename, 'w', encoding='utf-8')
                opened_listing_file = True
            except IOError as e:
                self.error_reporter.add_error(f"Cannot open listing file '{self.listing_filename}': {e}", 0, code='F')
                self._print_summary()
                return False

        opened_binary_file = False
        if self.binary_filename:
            try:
                self._binary_handle = open(self.binary_filename, 'w', encoding='utf-8')
                opened_binary_file = True
            except IOError as e:
                self.error_reporter.add_error(f"Cannot open binary file '{self.binary_filename}': {e}", 0, code='F')
                if opened_listing_file and self._listing_handle != sys.stdout:
                    self._listing_handle.close()
                self._print_summary()
                return False
        
        self.output_generator = OutputGenerator(
            listing_file_handle=self._listing_handle,
            binary_file_handle=self._binary_handle,
            assembler_ref=self
        )
        
        if self.debug_mode: print(f"Debug CRASS: AssemblerState ID in assemble(): {id(self.state)}")

        pass1_success = perform_pass(self, 1)
        if not pass1_success or self.error_reporter.has_errors():
            print("Assembly failed in Pass 1.")
            self._cleanup_files(opened_listing_file, opened_binary_file)
            self._print_summary()
            return False
        
        if self.debug_mode:
            print(f"Debug CRASS: Symbol table ID after Pass 1: {id(self.symbol_table)}")

        pass2_success = perform_pass(self, 2)
        if not pass2_success or self.error_reporter.has_errors():
            print("Assembly failed in Pass 2.")
            self._cleanup_files(opened_listing_file, opened_binary_file, generator_managed=True)
            self._print_summary()
            return False
        
        print("Assembly completed.")
        self._cleanup_files(opened_listing_file, opened_binary_file, generator_managed=True)
        self._print_summary()
        return not self.error_reporter.has_errors()

    def _read_input_file(self) -> bool:
        try:
            with open(self.input_filename, 'r', encoding='utf-8') as f:
                self.lines = f.readlines()
            found_end = any(line.upper().split() and line.upper().split()[0] in ("END", "ENDL") for line in self.lines)
            if not found_end:
                self.lines.append(" ENDL DUMMYEND")
                if self.debug_mode: print("DEBUG: Added dummy END line as no END directive was found.")
            self.lines = [line.rstrip('\n\r') for line in self.lines]
            return True
        except FileNotFoundError:
            self.error_reporter.add_error(f"Input file not found: {self.input_filename}", 0, code='F')
            return False
        except Exception as e:
            self.error_reporter.add_error(f"Error reading input file: {e}", 0, code='F')
            return False

    def _cleanup_files(self, opened_listing_file, opened_binary_file, generator_managed=False):
        if generator_managed and self.output_generator:
            pass
        else:
            if opened_listing_file and self._listing_handle != sys.stdout:
                self._listing_handle.close()
            if opened_binary_file and self._binary_handle:
                self._binary_handle.close()
        self._listing_handle = None
        self._binary_handle = None

    def _print_summary(self):
        print("\n--- Assembly Summary ---")
        # ErrorReporter.print_summary now handles its own output streams
        self.error_reporter.print_summary()

def main():
    parser = argparse.ArgumentParser(description=f"CRASS COMPASS Assembler v{VERSION}")
    parser.add_argument("input_file", help="COMPASS source file to assemble.")
    parser.add_argument("-l", "--listing", help="Output listing file name (defaults to stdout).")
    parser.add_argument("-o", "--output", help=f"Output binary file name (defaults to '{DEFAULT_BINARY_FILENAME}').")
    parser.add_argument("-d", "--debug", action="store_true", help="Enable debug mode.")
    args = parser.parse_args()

    assembler = Assembler(
        input_filename=args.input_file,
        listing_filename=args.listing,
        binary_filename=args.output,
        debug_mode=args.debug
    )
    exit_code = 0
    try:
        if not assembler.assemble():
            exit_code = 1
    except SystemExit as e:
         if isinstance(e.code, int): exit_code = e.code
         else: exit_code = 1
         print(f"Assembly aborted with exit code {exit_code}.")
    except Exception as e:
        print(f"CRITICAL UNHANDLED ERROR: {e}", file=sys.stderr)
        traceback.print_exc(file=sys.stderr)
        exit_code = 1
    finally:
        if hasattr(assembler, '_listing_handle') and assembler._listing_handle and \
           assembler._listing_handle != sys.stdout and not assembler._listing_handle.closed:
            assembler._listing_handle.close()
        if hasattr(assembler, '_binary_handle') and assembler._binary_handle and \
           not assembler._binary_handle.closed:
            assembler._binary_handle.close()

    print("Done.")
    sys.exit(exit_code)

if __name__ == "__main__":
    main()

# crass.py v1.96
