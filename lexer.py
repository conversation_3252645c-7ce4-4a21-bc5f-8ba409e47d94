# lexer.py v1.8
"""
Provides the line parsing functionality for the CRASS assembler,
adhering to the COMPASS fixed-field rules. Includes special handling
for pseudo-ops like DIS, TITLE, MICRO.
v1.7: - Modified label parsing to accommodate micro names on BASE/CODE lines.
        If a potential label is followed by BASE or CODE, it's treated as
        a label even if it doesn't start in column 1, provided it's a valid
        symbol format. (This introduced a regression for general labels).
v1.8: - Reverted label parsing logic to be closer to v1.6 to fix regression
        where valid labels were misparsed as opcodes.
        Correctly identifies labels starting in col 1 or after initial blanks
        if they are valid symbols. Special handling for BASE/CODE micro names
        will rely on downstream logic using the parsed label field.
"""
import re

# Known pseudo-ops that allow embedded blanks in their operand field
# or have unique operand/comment parsing rules.
PSEUDO_OPS_WITH_SPECIAL_OPERAND_HANDLING = {
    'DIS', 'TITLE', 'TTL', 'COMMENT', 'MICRO'
}
# CTEXT/XTEXT might need special handling too, but deferring for now.
# IFC is handled by its own parser in conditional_processing.py

def parse_line(line, line_num):
    line = line.rstrip()
    original_line = line

    fields = {
        'line_num': line_num,
        'original': original_line,
        'label': None,
        'opcode': None,
        'operand_str': None,
        'comment': None,
        'is_comment_line': False,
        'error': None
    }

    if not line: return fields
    if line.startswith('*'): # Rule 1
        fields['is_comment_line'] = True
        fields['comment'] = line[1:]
        return fields

    # --- Label Field ---
    # A label must start in column 1, or if column 1 is blank, in column 2.
    # It consists of 1-8 alphanumeric characters, first must be alphabetic.
    # Special labels '+' and '-' are also allowed.
    
    label_candidate = ""
    potential_label_end = 0
    
    # Check for label starting in column 1
    if len(line) > 0 and line[0] != ' ':
        potential_label_end = 0
        while potential_label_end < len(line) and line[potential_label_end] != ' ':
            potential_label_end += 1
        label_candidate = line[0:potential_label_end]
    # Else, check for label starting in column 2 (if col 1 is blank)
    elif len(line) > 1 and line[0] == ' ' and line[1] != ' ':
        potential_label_end = 1
        while potential_label_end < len(line) and line[potential_label_end] != ' ':
            potential_label_end += 1
        label_candidate = line[1:potential_label_end]

    if label_candidate:
        is_valid_symbol = re.fullmatch(r'[A-Za-z][A-Za-z0-9]{0,7}', label_candidate)
        is_plus_minus = label_candidate in ('+', '-')
        if is_valid_symbol or is_plus_minus:
            fields['label'] = label_candidate
            # Opcode search starts after this valid label
            current_pos = potential_label_end
        else:
            # Not a valid label format starting in the correct columns;
            # assume no label, and the first token is the opcode.
            fields['label'] = None
            current_pos = 0 # Start opcode search from beginning (after skipping initial blanks)
    else:
        # No token in label position, or line starts with multiple blanks.
        fields['label'] = None
        current_pos = 0 # Start opcode search from beginning (after skipping initial blanks)

    # --- Find Opcode Start ---
    opcode_start_col = current_pos
    while opcode_start_col < len(line) and line[opcode_start_col] == ' ':
        opcode_start_col += 1
    current_pos = opcode_start_col

    # --- Opcode Field ---
    if current_pos < len(line):
        end_pos_opcode = current_pos
        while end_pos_opcode < len(line) and line[end_pos_opcode] != ' ':
            end_pos_opcode += 1
        fields['opcode'] = line[current_pos:end_pos_opcode]
        current_pos = end_pos_opcode
    else: 
        # No opcode found. If there was a label, the rest is comment.
        # If no label and no opcode, it's a blank line (already handled) or malformed.
        if fields['label']: # Label-only line, or label then comment
            # The 'remainder_of_line' logic below will pick up the comment.
            pass
        else: # Truly blank or only spaces after potential label area
            return fields


    # --- Find Operand/Comment Start ---
    operand_comment_start_col = current_pos
    while operand_comment_start_col < len(line) and line[operand_comment_start_col] == ' ':
        operand_comment_start_col += 1
    
    remainder_of_line = ""
    if operand_comment_start_col < len(line):
        remainder_of_line = line[operand_comment_start_col:]

    # --- Operand/Address and Comment Fields ---
    opcode_upper_for_operand_logic = fields['opcode'].upper() if fields['opcode'] else ""

    if not fields['opcode'] and fields['label']:
        # Label-only line, remainder is comment
        if remainder_of_line: fields['comment'] = remainder_of_line
        return fields
    # If no opcode and no label, it's effectively a blank/comment line (or error)
    # This case should be rare if initial checks are fine.

    if opcode_upper_for_operand_logic in PSEUDO_OPS_WITH_SPECIAL_OPERAND_HANDLING:
        fields['operand_str'] = remainder_of_line
        fields['comment'] = None 
    elif remainder_of_line:
        end_pos_operand_in_remainder = 0
        while end_pos_operand_in_remainder < len(remainder_of_line) and \
              remainder_of_line[end_pos_operand_in_remainder] != ' ':
            end_pos_operand_in_remainder += 1
        
        fields['operand_str'] = remainder_of_line[:end_pos_operand_in_remainder]

        start_pos_comment_in_remainder = end_pos_operand_in_remainder
        while start_pos_comment_in_remainder < len(remainder_of_line) and \
              remainder_of_line[start_pos_comment_in_remainder] == ' ':
            start_pos_comment_in_remainder += 1
        
        if start_pos_comment_in_remainder < len(remainder_of_line):
            fields['comment'] = remainder_of_line[start_pos_comment_in_remainder:]
        else:
            fields['comment'] = None
    else:
        fields['operand_str'] = None
        fields['comment'] = None
        
    return fields

# lexer.py v1.8
