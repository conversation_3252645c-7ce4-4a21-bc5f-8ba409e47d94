# pass2_processing.py v3.80
"""
Contains the line processing logic for Pass 2 of the CRASS assembler.
Moved from pass_logic.py v2.0 as part of refactoring in v2.0.
... (previous version comments) ...
v3.79: Add handling for RMT/HERE in Pass 2 to list them correctly.
       Ensure macro/opdef definition lines are listed.
       Correctly handle macro/opdef expansion for listing.
       Integrate PSEUDO_SYMBOL_STRING_VALUE_INDICATOR for IDENT.
       Integrate ENDL_PROGRAM_LENGTH_INDICATOR for END/ENDL.
       Integrate CONDITIONAL_EXPR_VALUE_INDICATOR for IFxx ops.
v3.80: Correct import of MAX_BITS_IN_WORD from assembler_state.py.
"""
import re
import traceback
from typing import TYPE_CHECKING, Dict, Any, Optional, List, Tuple

if TYPE_CHECKING:
    from crass import Assembler
    from symbol_table import SymbolTable
    from instruction_table import InstructionTable
    from errors import Erro<PERSON><PERSON><PERSON>orter
    from assembler_state import AssemblerState
    from output_generator import OutputGenerator

from errors import AsmException
from expression import ExpressionError, substitute_micros
from operand_parser import OperandParseError
from pseudo_op_handlers import handle_pseudo_op_pass_2
from instruction_assembler import assemble_instruction
from output_generator import (
    PSEUDO_VALUE_WIDTH_INDICATOR, EQU_STAR_LC_INDICATOR,
    PSEUDO_SYMBOL_STRING_VALUE_INDICATOR, ENDL_PROGRAM_LENGTH_INDICATOR,
    CONDITIONAL_EXPR_VALUE_INDICATOR
)
# Corrected import for MAX_BITS_IN_WORD
from assembler_state import handle_force_upper, MAX_BITS_IN_WORD
from lexer import parse_line # For parsing macro/opdef body lines


def process_line_pass_2(
    state: 'AssemblerState',
    output_generator: 'OutputGenerator',
    assembler: 'Assembler',
    line_num: int,
    parsed_dict: Dict[str, Any],
    is_macro_expansion_line: bool = False, # True if this line is from a macro/opdef body
    macro_call_line_num: Optional[int] = None, # Original line number of the macro call
    macro_name_for_listing: Optional[str] = None, # Name of the macro being expanded
    macro_body_line_idx: Optional[int] = None # 1-based index of this line within macro body
    ) -> bool:

    symbol_table = assembler.symbol_table
    error_reporter = assembler.error_reporter
    instruction_table = assembler.instruction_table
    debug_mode = state.debug_mode

    source_line = parsed_dict['original']
    error_code = error_reporter.get_error_code_for_line(line_num if not is_macro_expansion_line else macro_call_line_num)
    
    listing_lc = state.line_start_address
    listing_pc_bits = state.line_start_position_bits
    
    label_on_line = parsed_dict['label']
    mnemonic_upper = (parsed_dict['opcode'] or "").upper()
    operand_str = parsed_dict.get('operand_str', "")

    if debug_mode:
        print(f"DEBUG P2 L{line_num} (Exp={is_macro_expansion_line}, ID(state)={id(state)}): ENTER. LC={listing_lc:o}, PC={listing_pc_bits}, Block='{state.current_block}', CODE='{state.current_code}'")
        print(f"    Parsed content: Label='{label_on_line}', Mnemonic='{mnemonic_upper}', Operand='{operand_str}'")

    # --- Preamble Mode Handling (already done in pass_logic.py, this is for main loop) ---
    if state.listing_preamble_mode: # Should not be true if called from main loop after preamble
        if debug_mode: print(f"DEBUG P2 L{line_num}: Still in preamble mode unexpectedly. Processing as such.")
        # Simplified preamble processing if this path is hit (it shouldn't be for main content)
        if mnemonic_upper in {"IDENT", "TITLE", "TTL", "LIST", "NOLIST", "ABS", "REL", "USE", "BASE", "CODE", "MACHINE", "CPU", "PPU", "CMU", "QUAL", "SPACE", "EJECT", "COMMENT"}:
            handle_pseudo_op_pass_2(assembler, line_num, mnemonic_upper, operand_str, label_on_line)
        output_generator.write_listing_line(line_num, None, None, None, source_line, error_code, state=state, pseudo_op_mnemonic=mnemonic_upper)
        return True

    # --- Conditional Assembly: Skip if condition is false ---
    if not state.conditional_stack[-1] and not is_macro_expansion_line: # Don't skip macro body lines based on outer conditional
        if debug_mode: print(f"DEBUG P2 L{line_num}: Line '{source_line.strip()}' SKIPPED due to false conditional.")
        output_generator.write_listing_line(
            line_num, listing_lc, listing_pc_bits, None, source_line, error_code,
            state=state, is_skipped=True, label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper
        )
        return True

    # --- SKIP pseudo-op ---
    if state.skip_count > 0 and not is_macro_expansion_line: # Don't skip macro body lines based on outer SKIP
        if mnemonic_upper != "END" and mnemonic_upper != "ENDL":
            if not parsed_dict.get('is_comment_line', False) and (label_on_line or mnemonic_upper or operand_str):
                state.skip_count -= 1
            if debug_mode: print(f"DEBUG P2 L{line_num}: Line '{source_line.strip()}' SKIPPED due to active SKIP. skip_count now {state.skip_count}")
            output_generator.write_listing_line(
                line_num, listing_lc, listing_pc_bits, None, source_line, error_code,
                state=state, is_skipped=True, label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper
            )
            return True
        # Allow END/ENDL to terminate skip
    
    # --- Handle Macro/Opdef Definition Lines (just list them) ---
    if state.is_defining and not is_macro_expansion_line: # We are inside a MACRO/OPDEF definition block
        if mnemonic_upper == "ENDM":
            state.is_defining = None
            state.current_definition_name = None
        output_generator.write_listing_line(line_num, listing_lc, listing_pc_bits, None, source_line, error_code, state=state, label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper)
        return True
    if mnemonic_upper in ("MACRO", "OPDEF") and not is_macro_expansion_line:
        state.is_defining = mnemonic_upper
        def_name = label_on_line
        if not def_name:
            parts = re.split(r'[,\s]+', operand_str, 1) if operand_str else []
            if parts: def_name = parts[0]
            elif operand_str: def_name = operand_str
        state.current_definition_name = def_name.upper() if def_name else "???"
        output_generator.write_listing_line(line_num, listing_lc, listing_pc_bits, None, source_line, error_code, state=state, label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper)
        return True
    if mnemonic_upper == "MICRO" and not is_macro_expansion_line: # Single line definition
        output_generator.write_listing_line(line_num, listing_lc, listing_pc_bits, None, source_line, error_code, state=state, label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper)
        return True


    # --- Actual Processing for Active Lines ---
    generated_values_for_listing: Optional[Union[List[Tuple[int, int]], int, str]] = []
    listing_handled_by_pseudo_op = False

    # --- Alignment for Labels (if not already handled by a data-generating op forcing new word) ---
    # This alignment primarily affects the LC for the listing and symbol definition.
    # Binary generation alignment is handled by add_parcel_to_binary_word or add_full_word_to_binary.
    if label_on_line and not is_macro_expansion_line: # Labels in macros don't force alignment of outer LC
        if label_on_line not in ('+', '-'):
            if state.position_counter != 0:
                if debug_mode: print(f"DEBUG P2 L{line_num}: Label '{label_on_line}' requires word alignment. PC was {state.position_counter}.")
                global_handle_force_upper(state, output_generator, error_reporter, line_num)
        elif label_on_line == '+':
            if state.position_counter != 0:
                if debug_mode: print(f"DEBUG P2 L{line_num}: '+' label requires word alignment. PC was {state.position_counter}.")
                global_handle_force_upper(state, output_generator, error_reporter, line_num)
        # '-' label alignment is handled by VFD itself if it's a VFD op.
        # For other ops, '-' label doesn't force word alignment but implies parcel alignment.
        # The listing_lc and listing_pc_bits should reflect the state *after* this alignment.
        listing_lc = state.location_counter
        listing_pc_bits = state.position_counter


    try:
        if mnemonic_upper:
            if instruction_table.is_pseudo_op(mnemonic_upper):
                pseudo_op_result = handle_pseudo_op_pass_2(assembler, line_num if not is_macro_expansion_line else macro_call_line_num, mnemonic_upper, operand_str, label_on_line)
                if pseudo_op_result is None: # Error in pseudo-op
                    error_code = error_code or "A"
                    generated_values_for_listing = []
                else:
                    generated_values_for_listing = pseudo_op_result
                    # DATA/CON/DIS/BSS/BSSZ/VFD Pass 2 handlers now manage their own binary output and LC advancement.
                    # Their return value is primarily for listing.
                    if mnemonic_upper in ("DATA", "CON", "DIS", "BSS", "BSSZ"): # VFD returns parcels
                        listing_handled_by_pseudo_op = True # Indicates listing is done by handler or special formatting needed

            elif mnemonic_upper in assembler.macro_definitions or \
                 (mnemonic_upper + "Q" in assembler.macro_definitions and assembler.macro_definitions[mnemonic_upper + "Q"].get('type') == 'OPDEF'):
                
                actual_def_name = mnemonic_upper
                if mnemonic_upper + "Q" in assembler.macro_definitions and assembler.macro_definitions[mnemonic_upper + "Q"].get('type') == 'OPDEF':
                    actual_def_name = mnemonic_upper + "Q"

                macro_def = assembler.macro_definitions.get(actual_def_name)
                if not macro_def: # Should not happen if logic above is correct
                    error_reporter.add_error(f"Internal: Macro/OPDEF definition for '{actual_def_name}' not found.", line_num, 'F')
                    return False

                if debug_mode: print(f">>>> P2_PROCESSING: ENTERING MACRO/OPDEF EXPANSION BLOCK <<<<")
                # List the macro call line itself
                output_generator.write_listing_line(line_num, listing_lc, listing_pc_bits, [], source_line, error_code, state=state, label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper)

                # Process macro body
                call_operands = [op.strip() for op in operand_str.split(',')] if operand_str else []
                formal_params = macro_def.get('params', [])
                param_map: Dict[str, str] = {}

                # Map call label and operands to formal parameters
                # Format 1 (OPDEF): Label OPDEF P1 -> Call: Label Op P1_actual -> P1=P1_actual
                # Format 2 (MACRO): Label MACRO P1,P2 -> Call: Label Op A1,A2 -> P1=A1, P2=A2
                # Format 3 (MACRO): Name MACRO P1,P2 -> Call: Op Name,A1,A2 -> P1=A1, P2=A2 (Name is part of call_operands[0])
                
                def_format = macro_def.get('definition_format', 1) # Default to format 1 (like OPDEF)

                if def_format == 1: # OPDEF like: JJQ OPDEF P1 -> Call: JJ ARG -> P1=ARG
                    if formal_params:
                        param_map[formal_params[0]] = operand_str # Entire operand string is the first param
                elif def_format == 2: # MACRO like: MYMAC MACRO P1,P2 -> Call: MYMAC A,B -> P1=A, P2=B
                    for i, formal_param in enumerate(formal_params):
                        if i < len(call_operands): param_map[formal_param] = call_operands[i]
                        else: param_map[formal_param] = "" # Default to blank if not enough actuals
                # Format 3 (Name in operand) is not explicitly handled by definition_format yet, assume like format 2 for now

                if debug_mode:
                    print(f">>>> P2_PROCESSING L{line_num}: Pre-Loop. Expanding {macro_def['type']} '{actual_def_name}'.")
                    print(f">>>> P2_PROCESSING L{line_num}: Pre-Loop. Call Label='{label_on_line}', Call Operands={call_operands}")
                    print(f">>>> P2_PROCESSING L{line_num}: Pre-Loop. Formal Params={formal_params}")
                    print(f">>>> P2_PROCESSING L{line_num}: Pre-Loop. Resulting Param Map: {param_map}")
                    print(f">>>> P2_PROCESSING L{line_num}: Pre-Loop. debug_mode value (from assembler.debug_mode): {assembler.debug_mode}")


                for idx, body_line_str in enumerate(macro_def.get('body', [])):
                    substituted_line = body_line_str
                    # Substitute formal parameters
                    for formal, actual in param_map.items():
                        # Regex to match whole word, case-insensitive for formal param
                        # Ensure formal param is not part of a larger word
                        # Pattern: \b means word boundary. (?i) for case-insensitive.
                        pattern = r'(?i)\b' + re.escape(formal) + r'\b'
                        substituted_line = re.sub(pattern, actual, substituted_line)
                    
                    # Substitute micros within the substituted line
                    substituted_line = substitute_micros(substituted_line, assembler, line_num) # Use original call line_num for error reporting context

                    if debug_mode:
                        print(f"      L{line_num}.{idx+1} Body line BEFORE ANY sub: '{body_line_str.strip()}' (debug_mode: {assembler.debug_mode})")
                        # No need to print intermediate formal param subs, just final
                        print(f"      L{line_num}.{idx+1} Body line AFTER ALL param sub: '{substituted_line.strip()}'")
                        print(f"      L{line_num}.{idx+1} Body line AFTER micro sub: '{substituted_line.strip()}'")

                    # Parse and process the substituted line
                    # Use a temporary line number for parsing, but original for error reporting context
                    parsed_body_line = parse_line(substituted_line, 0) # Line num 0 for temp parse
                    parsed_body_line['original'] = substituted_line # Store the fully substituted line
                    
                    # Recursive call to process_line_pass_2 for the macro body line
                    process_line_pass_2(state, output_generator, assembler, 0, parsed_body_line,
                                        is_macro_expansion_line=True,
                                        macro_call_line_num=line_num,
                                        macro_name_for_listing=actual_def_name,
                                        macro_body_line_idx=idx + 1)
                listing_handled_by_pseudo_op = True # Expansion handles its own listing lines

            else: # Machine Instruction
                details_list = instruction_table.get_instruction_details(mnemonic_upper)
                if details_list:
                    operand_str_no_comment = operand_str.split('.')[0].split('*')[0].strip()
                    parcels = assemble_instruction(mnemonic_upper, details_list, operand_str_no_comment, symbol_table, state, error_reporter, instruction_table, line_num, assembler)
                    if parcels is not None:
                        generated_values_for_listing = parcels
                        for value, width in parcels:
                            if state.position_counter + width > MAX_BITS_IN_WORD:
                                global_handle_force_upper(state, output_generator, error_reporter, line_num)
                            parcel_lc = state.location_counter
                            output_generator.add_parcel_to_binary_word(parcel_lc, value, width, target_block_name=state.current_block)
                            state.advance_lc(width)
                        base_mnemonic = instruction_table.get_base_mnemonic(mnemonic_upper)
                        if base_mnemonic in DEFERRED_FORCE_MNEMONICS_PASS1: # Use imported constant
                            if state.position_counter != 0:
                                global_handle_force_upper(state, output_generator, error_reporter, line_num)
                    else:
                        error_code = error_code or "A"
                        generated_values_for_listing = []
                        expected_width = parsed_dict.get('pass1_width_estimate', 0)
                        if expected_width > 0:
                            if state.position_counter + expected_width > MAX_BITS_IN_WORD: global_handle_force_upper(state, output_generator, error_reporter, line_num)
                            state.advance_lc(expected_width)
                else:
                    error_code = error_code or "U"
                    generated_values_for_listing = []
                    if not error_reporter.has_error_on_line(line_num):
                         error_reporter.add_error(f"Unknown mnemonic '{mnemonic_upper}'", line_num, code='U')
        elif label_on_line and not mnemonic_upper: # Label on a blank line (comment handled earlier)
            generated_values_for_listing = [] # No octal data
        elif not label_on_line and not mnemonic_upper and parsed_dict.get('operand_str', "").strip() and not parsed_dict.get('operand_str', "").strip().startswith(('.', '*')):
            error_code = error_code or "S"
            if not error_reporter.has_error_on_line(line_num): error_reporter.add_error("Missing mnemonic", line_num, code='S')
            generated_values_for_listing = []


        if output_generator and not listing_handled_by_pseudo_op:
            # Determine LC/PC for listing this specific line
            # listing_lc and listing_pc_bits were set at the start of the line, after label alignment
            # For most ops, these are correct. For ops that change LC (LOC, END), adjust.
            display_lc = listing_lc
            display_pc_bits = listing_pc_bits

            if mnemonic_upper == 'LOC':
                 display_lc = state.location_counter # Show the *new* absolute LC set by LOC
                 display_pc_bits = 0
            elif mnemonic_upper == 'END' or mnemonic_upper == 'ENDL':
                 # The value for END/ENDL is the program length, handled by ENDL_PROGRAM_LENGTH_INDICATOR
                 pass # listing_data_for_first_line will have the indicator
            elif mnemonic_upper in ('BSS', 'BSSZ'):
                 display_pc_bits = 0 # BSS/BSSZ are word aligned

            output_generator.write_listing_line(
                line_num, display_lc, display_pc_bits,
                generated_values_for_listing,
                source_line, error_code, state=state,
                label_on_line=label_on_line,
                pseudo_op_mnemonic=mnemonic_upper,
                is_continuation=is_macro_expansion_line, # Treat macro body lines as continuations for now
                macro_call_line_num=macro_call_line_num,
                macro_name_for_listing=macro_name_for_listing,
                macro_body_line_idx=macro_body_line_idx
            )
        return True

    except (ExpressionError, OperandParseError, SyntaxError, ValueError, TypeError, KeyError, AsmException) as e:
         err_code_final = getattr(e, 'code', 'A') if isinstance(e, AsmException) else 'A'
         if not error_reporter.has_error_on_line(line_num if not is_macro_expansion_line else macro_call_line_num):
             error_reporter.add_error(f"Pass 2 error: {e}", line_num if not is_macro_expansion_line else macro_call_line_num, code=err_code_final)
         if output_generator and not listing_handled_by_pseudo_op: # Avoid double listing if pseudo-op already listed
             output_generator.write_listing_line(
                 line_num, listing_lc, listing_pc_bits, [], source_line, err_code_final, state=state,
                 label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper,
                 is_continuation=is_macro_expansion_line,
                 macro_call_line_num=macro_call_line_num,
                 macro_name_for_listing=macro_name_for_listing,
                 macro_body_line_idx=macro_body_line_idx
             )
         return False
    except Exception as e:
         err_code_final = 'F'
         error_reporter.add_error(f"Unexpected Pass 2 error: {e}", line_num if not is_macro_expansion_line else macro_call_line_num, code=err_code_final); traceback.print_exc()
         if output_generator and not listing_handled_by_pseudo_op:
             output_generator.write_listing_line(
                 line_num, listing_lc, listing_pc_bits, [], source_line, err_code_final, state=state,
                 label_on_line=label_on_line, pseudo_op_mnemonic=mnemonic_upper,
                 is_continuation=is_macro_expansion_line,
                 macro_call_line_num=macro_call_line_num,
                 macro_name_for_listing=macro_name_for_listing,
                 macro_body_line_idx=macro_body_line_idx
             )
         return False

# pass2_processing.py v3.80
