# pseudo_op_handlers.py v2.82
"""
Handles pseudo-operation processing for CRASS during Pass 1 and Pass 2.
Moved from crass.py v1.79.
... (previous version comments) ...
v2.81: Add handling for PSEUDO_SYMBOL_STRING_VALUE_INDICATOR for IDENT in Pass 2.
       Correct END/ENDL handler to use ENDL_PROGRAM_LENGTH_INDICATOR.
       Correct conditional op handlers to use CONDITIONAL_EXPR_VALUE_INDICATOR.
v2.82: Correct import of CONDITIONAL_EXPR_VALUE_INDICATOR from output_generator.
"""
import re
import math
import traceback
import sys
from typing import List, Optional, Tuple, Dict, Any, TYPE_CHECKING

if TYPE_CHECKING:
    from crass import Assembler
    from symbol_table import SymbolTable
    from assembler_state import AssemblerState
    from errors import ErrorReporter
    from output_generator import OutputGenerator

from symbol_table import SymbolTable
from assembler_state import AssemblerState, handle_force_upper as global_handle_force_upper # Renamed to avoid conflict
from errors import Error<PERSON>eporter, AsmException
from expression import (
    evaluate_expression, evaluate_data_item, parse_dis_operands,
    generate_dis_words, ExpressionError, substitute_micros,
    DISPLAY_CODE_MAP, DISPLAY_CODE_ZERO, DISPLAY_CODE_BLANK,
    INTERNAL_CODE_ZERO, INTERNAL_CODE_BLANK
)
from output_generator import (
    OutputGenerator, LINES_PER_PAGE, PSEUDO_VALUE_WIDTH_INDICATOR,
    EQU_STAR_LC_INDICATOR, PSEUDO_SYMBOL_STRING_VALUE_INDICATOR,
    ENDL_PROGRAM_LENGTH_INDICATOR, CONDITIONAL_EXPR_VALUE_INDICATOR
)
from conditional_processing import evaluate_condition
# Import from pass_logic for _estimate_instruction_width_pass1, which is used for MACRO/OPDEF sizing
import pass_logic as pl # Use alias to avoid circular if pass_logic also imports this

MAX_BITS_IN_WORD = 60
MASK_60_BIT = (1 << 60) - 1

# --- Pass 1 Sizing Helper (formerly calculate_pseudo_op_size) ---
def handle_data_generating_op_pass1(assembler: 'Assembler', line_num: int, mnemonic: str, operand_str: str) -> Optional[int]:
    state = assembler.state
    symbol_table = assembler.symbol_table
    error_reporter = assembler.error_reporter
    debug_mode = assembler.debug_mode
    operand_str_parsed = operand_str

    if debug_mode:
        print(f"Debug L{line_num} POH P1 SizeCalc: Mnemonic='{mnemonic}', Operand='{operand_str_parsed}'")

    bits_generated = 0
    try:
        if mnemonic == "DATA" or mnemonic == "CON":
            operands = operand_str_parsed.split(',')
            num_operands = len([op for op in operands if op.strip()])
            bits_generated = num_operands * MAX_BITS_IN_WORD
        elif mnemonic == "DIS":
            subst_operand_str = substitute_micros(operand_str, assembler, line_num)
            dis_ops = parse_dis_operands(subst_operand_str, symbol_table, state, line_num, assembler)
            if dis_ops['format'] == 1:
                n_words = dis_ops['n']
                bits_generated = n_words * MAX_BITS_IN_WORD
            elif dis_ops['format'] == 2:
                input_string = dis_ops['string']
                num_chars = len(input_string) + 2
                chars_per_word = 10
                num_words = math.ceil(num_chars / chars_per_word)
                bits_generated = num_words * MAX_BITS_IN_WORD
        elif mnemonic == "VFD":
            fields = operand_str_parsed.split(',')
            for field in fields:
                field = field.strip();
                if not field: continue
                parts = field.split('/', 1)
                if len(parts) != 2: raise ExpressionError(f"Invalid VFD field: '{field}'")
                try:
                    width_val, width_type, _ = evaluate_expression(parts[0].strip(), symbol_table, state, line_num, assembler)
                    if width_type != 'absolute' or not isinstance(width_val, int) or not (0 < width_val <= MAX_BITS_IN_WORD):
                        raise ExpressionError(f"VFD width '{width_val}' out of range (1-60)")
                    bits_generated += width_val
                except Exception as e:
                    raise ExpressionError(f"Error parsing VFD field '{field}' for size: {e}") from e
        elif mnemonic in ["BSS", "BSSZ"]:
            try:
                value, val_type, _ = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
                if val_type != 'absolute': raise ExpressionError(f"{mnemonic} requires absolute value")
                if not isinstance(value, int) or value < 0: raise ExpressionError(f"{mnemonic} requires non-negative integer value")
                bits_generated = value * MAX_BITS_IN_WORD
            except ExpressionError as e:
                raise ExpressionError(f"Error parsing {mnemonic} operand '{operand_str}' for size: {e}") from e
        else:
            error_reporter.add_error(f"Internal: handle_data_generating_op_pass1 called for non-data op {mnemonic}", line_num, 'F')
            return None
        if debug_mode:
            print(f"Debug L{line_num} POH P1 SizeCalc: Mnemonic='{mnemonic}', Calculated Bits={bits_generated}")
        return bits_generated
    except (ExpressionError, AsmException) as e:
        if not error_reporter.has_error_on_line(line_num):
            error_reporter.add_error(f"Error calculating size for {mnemonic} operand '{operand_str}': {e}", line_num, code='E')
        return None
    except Exception as e:
        if not error_reporter.has_error_on_line(line_num):
            error_reporter.add_error(f"Unexpected error calculating size for {mnemonic} operand '{operand_str}': {e}", line_num, code='F')
        traceback.print_exc()
        return None

# --- Pass 1 Main Handler ---
def handle_pseudo_op_pass_1(assembler: 'Assembler', line_num: int, mnemonic: str, operand_str: str, label: Optional[str], label_defined_by_pseudo: bool, line_start_abs_lc: int, line_start_abs_pc: int) -> bool:
    state = assembler.state
    symbol_table = assembler.symbol_table
    error_reporter = assembler.error_reporter
    debug_mode = assembler.debug_mode

    if debug_mode: print(f"DEBUG POH P1 L{line_num}: Handling pseudo-op '{mnemonic}' (AbsLC={line_start_abs_lc:o}, AbsPC={line_start_abs_pc}, Block={state.current_block}, LOC_Abs={state.lc_is_absolute_due_to_loc})")

    operand_str_parsed = operand_str
    if mnemonic not in ('DIS', 'TITLE', 'TTL', 'COMMENT', 'CTEXT', 'XTEXT', 'MICRO', 'LIST', 'NOLIST', 'MACRO', 'OPDEF'):
         operand_str_parsed = operand_str.split('.')[0].split('*')[0].strip()
    elif mnemonic in ('LIST', 'NOLIST'):
         operand_str_parsed = operand_str.strip()

    if mnemonic == "QUAL":
         qual_name = operand_str_parsed.strip().upper()
         if not qual_name: error_reporter.add_error("QUAL requires an operand (name or *)", line_num, code='S'); return False
         if qual_name == '*': state.current_qualifier = None
         elif not re.fullmatch(r'[A-Z][A-Z0-9]{0,7}', qual_name): error_reporter.add_error(f"Invalid qualifier name '{qual_name}'", line_num, code='S'); return False
         else: state.current_qualifier = qual_name
         if debug_mode: print(f"DEBUG POH P1 L{line_num}: Set qualifier to '{state.current_qualifier}'")
         return True

    if mnemonic.startswith("IF") or mnemonic == "ELSE" or mnemonic == "ENDIF":
        return handle_conditional_op_pass_1(assembler, line_num, mnemonic, operand_str_parsed)

    if not state.conditional_stack[-1]:
        if debug_mode: print(f"DEBUG POH P1 L{line_num}: Skipping pseudo-op '{mnemonic}' due to false conditional.")
        return True

    if mnemonic == "IDENT":
        program_name = operand_str_parsed.strip().upper()
        if not program_name: error_reporter.add_error("IDENT requires a program name.", line_num, code='S'); return False
        if label: error_reporter.add_warning(f"Label '{label}' ignored on IDENT statement.", line_num, code='W')
        attrs = {'type': 'absolute', 'redefinable': False, 'block': '*ABS*', 'program_name': True}
        if not symbol_table.define(program_name, 0, line_num, attrs, current_qualifier=None): return False
        if not state.first_title_directive_encountered:
            state.current_title = program_name
            state.first_title_directive_encountered = True
        return True
    elif mnemonic == "EQU" or mnemonic == '=':
        if operand_str_parsed.strip() == '*':
            if not label: error_reporter.add_error("EQU * requires a label", line_num, code='S'); return False
            value = state.location_counter
            pc_at_def = state.position_counter
            block_ctx = state.pre_loc_block_name if state.lc_is_absolute_due_to_loc and state.pre_loc_block_name else state.current_block
            val_type = 'absolute' if state.lc_is_absolute_due_to_loc or block_ctx == '*ABS*' else 'relocatable'
            if pc_at_def != 0: error_reporter.add_warning(f"EQU * label '{label}' is not word-aligned (PC={pc_at_def})", line_num, code='A')
            attrs = {'type': val_type, 'redefinable': False, 'block': block_ctx, 'equ_star': True}
            if not symbol_table.define(label, value, line_num, attrs, state.current_qualifier): return False
            return True
        if not label: error_reporter.add_error("EQU requires a label", line_num, code='S'); return False
        try:
            value, val_type, val_block = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
            block = val_block if val_block is not None else (state.current_block if val_type == 'relocatable' else '*ABS*')
            attrs = {'type': val_type, 'redefinable': False, 'block': block}
            if not symbol_table.define(label, value, line_num, attrs, state.current_qualifier): return False
        except (ExpressionError, AsmException) as e: error_reporter.add_error(f"EQU error: {e}", line_num, code='E'); return False
        return True
    elif mnemonic == "SET":
        if not label: error_reporter.add_error("SET requires a label", line_num, code='S'); return False
        try:
            value, val_type, val_block = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
            block = val_block if val_block is not None else (state.current_block if val_type == 'relocatable' else '*ABS*')
            attrs = {'type': val_type, 'redefinable': True, 'block': block}
            if not symbol_table.define(label, value, line_num, attrs, state.current_qualifier): return False
        except (ExpressionError, AsmException) as e: error_reporter.add_error(f"SET error: {e}", line_num, code='E'); return False
        return True
    elif mnemonic == "LOC":
         try:
            abs_addr_value, val_type, _ = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
            if val_type != 'absolute': raise ExpressionError("LOC requires absolute value")
            if abs_addr_value < 0: raise ExpressionError("LOC address cannot be negative")
            if state.position_counter != 0:
                 global_handle_force_upper(state, None, error_reporter, line_num, increment_block_size_on_force=True)
            if label:
                 attrs = {'type': 'absolute', 'redefinable': False, 'block': '*ABS*', 'defined_by_loc': True}
                 if not symbol_table.define(label, abs_addr_value, line_num, attrs, state.current_qualifier): return False
            state.set_location_counter(abs_addr_value, 0, is_loc_directive=True)
         except (ExpressionError, AsmException) as e: error_reporter.add_error(f"LOC error: {e}", line_num, code='E'); return False
         return True
    elif mnemonic in ["DATA", "CON", "DIS", "BSS", "BSSZ", "VFD"]:
        if state.position_counter != 0:
            global_handle_force_upper(state, None, error_reporter, line_num, increment_block_size_on_force=True)
        estimated_bits = handle_data_generating_op_pass1(assembler, line_num, mnemonic, operand_str_parsed)
        if estimated_bits is None: return False
        if estimated_bits > 0: state.advance_lc(estimated_bits)
        return True
    elif mnemonic == "LIT":
         if state.position_counter != 0: global_handle_force_upper(state, None, error_reporter, line_num, increment_block_size_on_force=True)
         try:
             literal_values_str = operand_str_parsed.split(',')
             for lit_str in literal_values_str:
                  lit_str = lit_str.strip();
                  if not lit_str: continue
                  lit_value, lit_type, _ = evaluate_data_item(lit_str, symbol_table, state, line_num, assembler)
                  if lit_type != 'absolute': raise ExpressionError(f"Literal must be absolute: '{lit_str}'")
                  symbol_table.add_literal(lit_value, line_num)
         except (ExpressionError, AsmException) as e: error_reporter.add_error(f"LIT error: {e}", line_num, code='E'); return False
         return True
    elif mnemonic == "BASE":
        parts = operand_str.split(maxsplit=1)
        base_mode_str = ""; micro_name = None
        if len(parts) >= 1 and re.fullmatch(r'[A-Za-z][A-Za-z0-9]{0,7}', parts[0]):
             if len(parts) == 2: micro_name = parts[0].upper(); base_mode_str = parts[1].strip().upper()
             else: base_mode_str = parts[0].strip().upper()
        elif len(parts) >= 1: base_mode_str = operand_str.strip().upper()
        if micro_name:
             attrs = {'type': 'absolute', 'value_is_char': True, 'redefinable': True, 'block': '*ABS*'}
             if not symbol_table.define(micro_name, state.current_base, line_num, attrs, state.current_qualifier): return False
        if base_mode_str:
            base_char = base_mode_str[0]
            if base_char in ['O', 'D', 'H', 'M']: state.set_base(base_char)
            elif base_char == '*': state.set_base('D')
            else: error_reporter.add_error(f"Invalid base: '{base_mode_str}'", line_num, code='V'); return False
        elif not micro_name: error_reporter.add_error("BASE requires an operand", line_num, code='S'); return False
        return True
    elif mnemonic == "CODE":
         code_char = operand_str_parsed.strip().upper()
         state.set_code(code_char)
         return True
    elif mnemonic == "USE":
         block_name = operand_str_parsed.strip().upper()
         if not block_name: error_reporter.add_error("USE requires a block name", line_num, code='S'); return False
         if state.position_counter != 0: global_handle_force_upper(state, None, error_reporter, line_num, increment_block_size_on_force=True)
         state.switch_block(block_name)
         return True
    elif mnemonic == "ABS":
         if state.position_counter != 0: global_handle_force_upper(state, None, error_reporter, line_num, increment_block_size_on_force=True)
         state.switch_block("*ABS*")
         return True
    elif mnemonic == "REL":
         if state.position_counter != 0: global_handle_force_upper(state, None, error_reporter, line_num, increment_block_size_on_force=True)
         state.switch_block(state.current_block if state.current_block != '*ABS*' else "*REL*")
         state.absolute_mode = False
         return True
    elif mnemonic == "LIST": state.update_listing_flags(operand_str_parsed, turn_on=True); return True
    elif mnemonic == "NOLIST": state.update_listing_flags(operand_str_parsed, turn_on=False); return True
    elif mnemonic == "TITLE":
        if not state.first_title_directive_encountered:
            title_text = operand_str.strip()
            title_text = re.split(r'\s+(\.|\*)', title_text, maxsplit=1)[0].strip()
            state.current_title = title_text
            state.first_title_directive_encountered = True
        return True
    elif mnemonic == "TTL":
        ttl_text = operand_str.strip()
        ttl_text = re.split(r'\s+(\.|\*)', ttl_text, maxsplit=1)[0].strip()
        state.current_ttl_title = ttl_text
        state.first_title_directive_encountered = True
        return True
    elif mnemonic == "SKIP":
         try:
             value, val_type, _ = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
             if val_type != 'absolute': raise ExpressionError("SKIP requires absolute value")
             if not isinstance(value, int) or value < 0: raise ExpressionError("SKIP requires non-negative integer")
             state.skip_count = value
         except (ExpressionError, AsmException) as e: error_reporter.add_error(f"SKIP error: {e}", line_num, code='E'); return False
         return True
    elif mnemonic == "END" or mnemonic == "ENDL": # ENDL is just a label for END
         state.end_statement_processed = True
         if label: assembler.end_statement_label = label
         start_symbol = operand_str_parsed.strip().upper()
         if start_symbol: state.program_start_symbol = start_symbol
         return True
    elif mnemonic in ("MACHINE", "CPU", "PPU", "CMU", "SPACE", "EJECT", "COMMENT", "ERROR", "FIN", "REF", "NOREF", "XREF", "SEQ", "UNL", "CTEXT", "ENDX", "RMT", "HERE", "EXT", "ENTRY", "LOCAL", "IRP", "ENDD", "DUP", "ECHO", "PURGE", "OPSYN", "DECMIC", "OCTMIC", "ENDMIC", "B1=1", "B7=1", "CHAR", "CPOP", "CPSYN", "ENTRYC", "ERRMI", "ERRNG", "ERRNZ", "ERRPL", "ERRZR", "LCC", "NIL", "NOLABEL", "PURGDEF", "PURGMAC", "REP", "REPC", "REPI", "R=", "SEG", "SEGMENT", "SST", "STEXT", "STOPDUP", "USELCM", "POS", "MAX", "MIN", "MICCNT"):
        if mnemonic == "ENTRY":
             entry_names = operand_str_parsed.split(',')
             for name_entry in entry_names:
                  name_entry = name_entry.strip().upper();
                  if name_entry: symbol_table.mark_entry_point(name_entry, line_num)
        return True
    error_reporter.add_warning(f"Pseudo-op '{mnemonic}' not fully handled in Pass 1", line_num, code='W')
    return True

# --- Pass 2 Main Handler ---
def handle_pseudo_op_pass_2(assembler: 'Assembler', line_num: int, mnemonic: str, operand_str: str, label: Optional[str]) -> Optional[List[Tuple[int, int]]]:
    state = assembler.state
    symbol_table = assembler.symbol_table
    error_reporter = assembler.error_reporter
    output_generator = assembler.output_generator
    debug_mode = assembler.debug_mode

    if debug_mode: print(f"DEBUG POH P2 L{line_num}: Handling pseudo-op '{mnemonic}'")
    generated_values: List[Tuple[int, int]] = []

    if mnemonic.startswith("IF") or mnemonic == "ELSE" or mnemonic == "ENDIF":
        return handle_conditional_op_pass_2(assembler, line_num, mnemonic, operand_str)

    if not state.conditional_stack[-1]:
        if debug_mode: print(f"DEBUG POH P2 L{line_num}: Skipping pseudo-op '{mnemonic}' due to false conditional.")
        return []

    operand_str_parsed = operand_str
    if mnemonic not in ('DIS', 'TITLE', 'TTL', 'COMMENT', 'CTEXT', 'XTEXT', 'MICRO', 'LIST', 'NOLIST', 'MACRO', 'OPDEF'):
         operand_str_parsed = operand_str.split('.')[0].split('*')[0].strip()
    elif mnemonic in ('LIST', 'NOLIST'):
         operand_str_parsed = operand_str.strip()

    if mnemonic == "QUAL":
         qual_name = operand_str_parsed.strip().upper()
         if not qual_name: return None
         if qual_name == '*': state.current_qualifier = None
         elif not re.fullmatch(r'[A-Z][A-Z0-9]{0,7}', qual_name): return None
         else: state.current_qualifier = qual_name
         return []
    if mnemonic == "IDENT":
        prog_attrs = symbol_table.get_program_name_attributes()
        if prog_attrs:
            prog_name = prog_attrs['name']
            return [(prog_name, PSEUDO_SYMBOL_STRING_VALUE_INDICATOR)]
        return [("ERROR", PSEUDO_SYMBOL_STRING_VALUE_INDICATOR)]
    elif mnemonic == "EQU" or mnemonic == '=':
        try:
            operand_expr = operand_str_parsed.strip()
            value = 0; is_equ_star = False
            if operand_expr == '*':
                is_equ_star = True
                if not label: return None
                sym_entry = symbol_table.lookup(label, line_num, state.current_qualifier)
                if sym_entry: value = sym_entry['value']
                else: error_reporter.add_error(f"EQU* label '{label}' not found in Pass 2", line_num, 'F'); return None
            else:
                value, _, _ = evaluate_expression(operand_expr, symbol_table, state, line_num, assembler)
            indicator = EQU_STAR_LC_INDICATOR if is_equ_star else PSEUDO_VALUE_WIDTH_INDICATOR
            return [(value, indicator)]
        except (ExpressionError, AsmException) as e:
            if not error_reporter.has_error_on_line(line_num): error_reporter.add_error(f"EQU error: {e}", line_num, code='E');
            return None
    elif mnemonic == "SET":
         if label:
              try:
                   value, _, _ = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
                   if value is None: return None
                   return [(value, PSEUDO_VALUE_WIDTH_INDICATOR)]
              except (ExpressionError, AsmException) as e:
                   if not error_reporter.has_error_on_line(line_num): error_reporter.add_error(str(e), line_num, code='E')
                   return None
         return None
    elif mnemonic == "DATA" or mnemonic == "CON":
         if state.position_counter != 0: global_handle_force_upper(state, output_generator, error_reporter, line_num)
         start_lc = state.location_counter
         expressions = operand_str_parsed.split(',')
         for expr_str in expressions:
              expr_str = expr_str.strip();
              if not expr_str: continue
              try:
                   value, val_type, _ = evaluate_data_item(expr_str, symbol_table, state, line_num, assembler)
                   if value is None: generated_values.append((0,60)); continue # Error already reported
                   if val_type != 'absolute': error_reporter.add_warning(f"{mnemonic} value '{expr_str}' non-absolute type '{val_type}'", line_num, "R")
                   if isinstance(value, int) and value < 0: value = (~abs(value)) & MASK_60_BIT
                   else: value &= MASK_60_BIT
                   generated_values.append((value, 60))
                   if output_generator: output_generator.add_full_word_to_binary(state.location_counter, value, is_data=True, target_block_name=state.current_block)
                   state.advance_lc(60)
              except (ExpressionError, AsmException) as e:
                   if not error_reporter.has_error_on_line(line_num): error_reporter.add_error(f"Error {mnemonic} expr '{expr_str}': {e}", line_num, code='E');
                   generated_values.append((0, 60)) # Add placeholder on error
                   if output_generator: output_generator.add_full_word_to_binary(state.location_counter, 0, is_data=True, target_block_name=state.current_block)
                   state.advance_lc(60)
         return generated_values # For listing, binary already written
    elif mnemonic == "DIS":
         if state.position_counter != 0: global_handle_force_upper(state, output_generator, error_reporter, line_num)
         start_lc = state.location_counter
         try:
             subst_operand_str = substitute_micros(operand_str, assembler, line_num)
             dis_ops = parse_dis_operands(subst_operand_str, symbol_table, state, line_num, assembler)
             gen_words = generate_dis_words(dis_ops, error_reporter, line_num, state)
             for word_value in gen_words:
                  generated_values.append((word_value, 60))
                  if output_generator: output_generator.add_full_word_to_binary(state.location_counter, word_value, is_data=True, target_block_name=state.current_block)
                  state.advance_lc(60)
             return generated_values
         except (ExpressionError, AsmException) as e:
             if not error_reporter.has_error_on_line(line_num): error_reporter.add_error(f"DIS error: {e}", line_num, code='E');
             # Generate one error word
             if output_generator: output_generator.add_full_word_to_binary(state.location_counter, 0, is_data=True, target_block_name=state.current_block)
             state.advance_lc(60)
             return [(0,60)] # Return one error word for listing
    elif mnemonic == "VFD":
         if label == '-':
              if state.position_counter % 15 != 0:
                   bits_to_pad = 15 - (state.position_counter % 15)
                   if output_generator:
                        noop_15 = 0o46000; temp_lc = state.location_counter; temp_pc = state.position_counter; bits_padded = 0
                        while bits_padded < bits_to_pad:
                             pad_width = min(15, bits_to_pad - bits_padded)
                             if temp_pc + pad_width > MAX_BITS_IN_WORD: global_handle_force_upper(state, output_generator, error_reporter, line_num); temp_lc = state.location_counter; temp_pc = state.position_counter
                             output_generator.add_parcel_to_binary_word(temp_lc, noop_15, pad_width, target_block_name=state.current_block)
                             temp_pc += pad_width
                             if temp_pc == MAX_BITS_IN_WORD: temp_lc += 1; temp_pc = 0
                             bits_padded += pad_width
                   state.advance_lc(bits_to_pad)
         vfd_parcels = generate_vfd_parcels(assembler, line_num, operand_str)
         if vfd_parcels is None: return None
         return vfd_parcels # Parcels handled by main loop for binary and listing
    elif mnemonic in ["BSS", "BSSZ"]:
         if state.position_counter != 0: global_handle_force_upper(state, output_generator, error_reporter, line_num)
         try:
             value, val_type, _ = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
             if val_type != 'absolute' or not isinstance(value, int) or value < 0: raise ExpressionError(f"{mnemonic} requires non-negative absolute integer")
             num_words = value
             if mnemonic == "BSSZ" and output_generator:
                  for _ in range(num_words):
                       output_generator.add_full_word_to_binary(state.location_counter, 0, is_data=True, target_block_name=state.current_block)
                       state.advance_lc(60)
             else: # BSS or BSSZ without binary output
                  state.advance_lc(num_words * MAX_BITS_IN_WORD)
             return [(value, PSEUDO_VALUE_WIDTH_INDICATOR)]
         except (ExpressionError, AsmException) as e:
             if not error_reporter.has_error_on_line(line_num): error_reporter.add_error(str(e), line_num, code='E')
             return None
    elif mnemonic == "LOC":
         try:
             value, val_type, _ = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
             if val_type != 'absolute': raise ExpressionError("LOC requires absolute value")
             if value < 0: raise ExpressionError("LOC address cannot be negative")
             if output_generator: output_generator.flush_binary_word(pad_with_noops=True, target_block_name=state.current_block) # Flush current block
             if state.position_counter != 0: state.location_counter += 1; state.position_counter = 0
             state.set_location_counter(value, 0, is_loc_directive=True)
             state.current_block = '*ABS*' # LOC implies *ABS* context for subsequent lines
             state.absolute_mode = True
             return []
         except (ExpressionError, AsmException) as e:
             if not error_reporter.has_error_on_line(line_num): error_reporter.add_error(str(e), line_num, code='E')
             return None
    elif mnemonic == "BASE":
        parts = operand_str.split(maxsplit=1)
        base_mode_str = (parts[1] if len(parts) == 2 and re.fullmatch(r'[A-Za-z][A-Za-z0-9]{0,7}', parts[0]) else operand_str).strip().upper()
        if not base_mode_str: return None
        base_char = base_mode_str[0]
        if base_char in ['O', 'D', 'H', 'M']: state.set_base(base_char)
        elif base_char == '*': state.set_base('D')
        else: return None
        return []
    elif mnemonic == "CODE":
         code_char = operand_str_parsed.strip().upper()
         if code_char in ['D', 'A', 'I', 'E']: state.set_code(code_char)
         else: return None
         return []
    elif mnemonic == "USE":
         block_name = operand_str_parsed.strip().upper()
         if not block_name: return None
         if output_generator: output_generator.flush_binary_word(pad_with_noops=True, target_block_name=state.current_block)
         state.switch_block(block_name)
         return []
    elif mnemonic == "ABS":
         if output_generator: output_generator.flush_binary_word(pad_with_noops=True, target_block_name=state.current_block)
         if state.position_counter != 0: global_handle_force_upper(state, output_generator, error_reporter, line_num)
         state.switch_block("*ABS*")
         return []
    elif mnemonic == "REL":
         if output_generator: output_generator.flush_binary_word(pad_with_noops=True, target_block_name=state.current_block)
         if state.position_counter != 0: global_handle_force_upper(state, output_generator, error_reporter, line_num)
         state.switch_block(state.current_block if state.current_block != '*ABS*' else "*REL*")
         state.absolute_mode = False
         return []
    elif mnemonic == "SKIP":
         try:
             value, val_type, _ = evaluate_expression(operand_str_parsed, symbol_table, state, line_num, assembler)
             if val_type != 'absolute' or not isinstance(value, int) or value < 0: raise ExpressionError("SKIP requires non-negative absolute integer")
             state.skip_count = value
         except (ExpressionError, AsmException) as e:
             if not error_reporter.has_error_on_line(line_num): error_reporter.add_error(f"SKIP error: {e}", line_num, code='E')
             return None
         return []
    elif mnemonic == "END" or mnemonic == "ENDL":
         state.end_statement_processed = True
         start_symbol = operand_str_parsed.strip().upper()
         prog_len = assembler.total_program_length if hasattr(assembler, 'total_program_length') else state.location_counter
         if start_symbol:
              state.program_start_symbol = start_symbol
              start_addr_info = symbol_table.lookup(start_symbol, line_num, state.current_qualifier)
              if start_addr_info:
                   start_addr = start_addr_info['value']
                   start_block = start_addr_info['attrs'].get('block')
                   start_type = start_addr_info['attrs'].get('type')
                   if start_type == 'relocatable' and start_block and start_block != '*ABS*':
                        block_base = assembler.block_base_addresses.get(start_block, 0)
                        start_addr += block_base
                   state.program_start_address = start_addr
              else: state.program_start_address = 0 # Default on error
         else: # Default to program name or 0
              prog_attrs = symbol_table.get_program_name_attributes()
              if prog_attrs: state.program_start_symbol = prog_attrs['name']; state.program_start_address = prog_attrs['value']
              else: state.program_start_symbol = None; state.program_start_address = 0
         return [(prog_len, ENDL_PROGRAM_LENGTH_INDICATOR)]
    elif mnemonic in ("LIST", "NOLIST", "TITLE", "TTL", "SPACE", "EJECT", "COMMENT", "ERROR", "FIN", "REF", "NOREF", "XREF", "SEQ", "MACHINE", "CPU", "PPU", "CMU", "UNL", "CTEXT", "ENDX", "RMT", "HERE", "EXT", "ENTRY", "LOCAL", "IRP", "ENDD", "DUP", "ECHO", "PURGE", "OPSYN", "DECMIC", "OCTMIC", "ENDMIC", "B1=1", "B7=1", "CHAR", "CPOP", "CPSYN", "ENTRYC", "ERRMI", "ERRNG", "ERRNZ", "ERRPL", "ERRZR", "LCC", "NIL", "NOLABEL", "PURGDEF", "PURGMAC", "REP", "REPC", "REPI", "R=", "SEG", "SEGMENT", "SST", "STEXT", "STOPDUP", "USELCM", "POS", "MAX", "MIN", "MICCNT"):
        if mnemonic == "TITLE":
            title_text = operand_str.strip(); title_text = re.split(r'\s+(\.|\*)', title_text, maxsplit=1)[0].strip()
            state.current_title = title_text
        elif mnemonic == "TTL":
            ttl_text = operand_str.strip(); ttl_text = re.split(r'\s+(\.|\*)', ttl_text, maxsplit=1)[0].strip()
            state.current_ttl_title = ttl_text
        elif mnemonic == "SPACE":
            count = 1
            try:
                parts = operand_str_parsed.split(',');
                if parts[0]: count = int(parts[0])
                if count < 0: count = 1
            except (ValueError, IndexError): count = 1
            if output_generator:
                try: output_generator.listing_file.write("\n" * count); output_generator.lines_on_page += count
                except IOError: pass
        elif mnemonic == "EJECT":
            if output_generator: output_generator.lines_on_page = LINES_PER_PAGE + 1
        elif mnemonic == "LIST": state.update_listing_flags(operand_str_parsed, turn_on=True)
        elif mnemonic == "NOLIST": state.update_listing_flags(operand_str_parsed, turn_on=False)
        return []
    error_reporter.add_warning(f"Pseudo-op '{mnemonic}' not fully handled in Pass 2", line_num, code='W')
    return []


def handle_conditional_op_pass_1(assembler: 'Assembler', line_num: int, mnemonic: str, operand_str: str) -> bool:
    state = assembler.state
    error_reporter = assembler.error_reporter
    debug_mode = assembler.debug_mode
    currently_active_outer = not state.conditional_stack or state.conditional_stack[-1]
    eval_result = False
    if mnemonic.startswith("IF"):
        if currently_active_outer:
            try: eval_result = evaluate_condition(assembler, line_num, mnemonic, operand_str)
            except (ExpressionError, AsmException) as e: eval_result = False # Error reported by evaluate_condition
        state.conditional_stack.append(currently_active_outer and eval_result)
    elif mnemonic == "ELSE":
        if not state.conditional_stack: error_reporter.add_error("ELSE without matching IF", line_num, 'S'); return False
        outer_active = state.conditional_stack[-2] if len(state.conditional_stack) > 1 else True
        if_branch_was_true = state.conditional_stack.pop()
        state.conditional_stack.append(outer_active and not if_branch_was_true)
    elif mnemonic == "ENDIF":
        if not state.conditional_stack: error_reporter.add_error("ENDIF without matching IF", line_num, 'S'); return False
        state.conditional_stack.pop()
    if not state.conditional_stack: state.conditional_stack.append(True) # Ensure stack is never empty
    return True


def handle_conditional_op_pass_2(assembler: 'Assembler', line_num: int, mnemonic: str, operand_str: str) -> Optional[List[Tuple[int, int]]]:
    state = assembler.state
    error_reporter = assembler.error_reporter
    debug_mode = assembler.debug_mode
    currently_active_outer = not state.conditional_stack or state.conditional_stack[-1]
    eval_result = False
    evaluated_expr_value_for_listing = None

    if mnemonic.startswith("IF"):
        if currently_active_outer:
            try:
                eval_result, evaluated_expr_value_for_listing = evaluate_condition(assembler, line_num, mnemonic, operand_str, return_expr_val=True)
            except (ExpressionError, AsmException) as e:
                eval_result = False # Error reported by evaluate_condition
        new_stack_state = currently_active_outer and eval_result
        state.conditional_stack.append(new_stack_state)
    elif mnemonic == "ELSE":
        if len(state.conditional_stack) <= 1: return None # Error already reported
        outer_active = state.conditional_stack[-2] if len(state.conditional_stack) > 1 else True
        if_branch_was_true = state.conditional_stack.pop()
        new_stack_state = outer_active and not if_branch_was_true
        state.conditional_stack.append(new_stack_state)
    elif mnemonic == "ENDIF":
        if len(state.conditional_stack) <= 1: return None # Error already reported
        state.conditional_stack.pop()

    if not state.conditional_stack: state.conditional_stack.append(True)

    if evaluated_expr_value_for_listing is not None:
        return [(evaluated_expr_value_for_listing, CONDITIONAL_EXPR_VALUE_INDICATOR)]
    return []

# pseudo_op_handlers.py v2.82
