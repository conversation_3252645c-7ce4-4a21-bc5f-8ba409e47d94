# assembler_state.py v1.68
"""
Manages the state of the assembler during passes, including Location Counter (LC),
Position Counter (PC), current block, base, listing flags, etc.
v1.66: Add deferred_force_upper_pending flag and logic to handle it.
       Ensure force_upper is only called when PC != 0.
       Corrected logic for handling labels that force alignment.
       Added pre_loc_block_name to track block before LOC.
       Corrected block_lcs update in advance_lc and force_upper.
       Refined switch_block for Pass 1 to correctly use block_lcs.
       Added lc_is_absolute_due_to_loc flag.
v1.67: Add detailed debug print in set_location_counter to verify block_lcs update.
       Restore FORCE_NEW_WORD_MNEMONICS_PASS1 and DEFERRED_FORCE_MNEMONICS_PASS1.
v1.68: Add first_title_directive_encountered attribute.
"""
from typing import Optional, Dict, List, Tuple, Union, TYPE_CHECKING

if TYPE_CHECKING:
    from errors import ErrorReporter
    from output_generator import OutputGenerator
    from crass import Assembler

# --- Constants ---
MAX_BITS_IN_WORD = 60

FORCE_NEW_WORD_MNEMONICS_PASS1 = {"DATA", "CON", "DIS", "VFD", "BSS", "BSSZ", "LIT", "MICRO"}
DEFERRED_FORCE_MNEMONICS_PASS1 = {"JP", "RJ", "XJ", "PS", "EQ", "ZR", "NZ", "PL", "NG", "NE", "GE", "IR", "OR", "DF", "ID"}


# --- Global Helper Function (moved from pass_logic.py) ---
def handle_force_upper(
    assembler_state: 'AssemblerState',
    output_gen: Optional['OutputGenerator'],
    error_reporter: 'ErrorReporter',
    line_num: int,
    consume_deferred_flag: bool = True,
    increment_block_size_on_force: bool = True
):
    if assembler_state.debug_mode:
        print(f">>> DEBUG LC: L{line_num} handle_force_upper: Forcing upper from PC={assembler_state.position_counter}, DeferredPending={assembler_state.deferred_force_upper_pending}, ConsumeFlag={consume_deferred_flag}, IncrBlkSize={increment_block_size_on_force}")

    if assembler_state.position_counter != 0:
        if assembler_state.pass_number == 2 and output_gen:
            is_data_type = output_gen.get_buffer_is_data_type(assembler_state.current_block)
            output_gen.flush_binary_word(pad_with_noops=(is_data_type is False), target_block_name=assembler_state.current_block)
        assembler_state.force_upper(increment_block_size=increment_block_size_on_force)
    if consume_deferred_flag:
        assembler_state.deferred_force_upper_pending = False


class AssemblerState:
    """Tracks the assembler's current state."""
    def __init__(self, error_reporter=None, symbol_table=None, debug_mode=False, assembler_ref=None):
        self.location_counter: int = 0
        self.position_counter: int = 0
        self.current_block: str = '*ABS*'
        self.current_base: str = 'D'
        self.current_code: str = 'D'
        self.listing_flags: Dict[str, bool] = {
            'B': True, 'C': True, 'D': True, 'E': True, 'F': True, 'G': True,
            'M': True, 'N': True, 'R': True, 'S': True, 'X': True
        }
        self.current_qualifier: Optional[str] = None
        self.pass_number: int = 0
        self.absolute_mode: bool = True
        self.conditional_stack: List[bool] = [(True)]
        self.end_statement_processed: bool = False
        self.current_line_number: int = 0
        self.line_start_address: int = 0
        self.line_start_position_bits: int = 0
        self.program_start_symbol: Optional[str] = None
        self.program_start_address: Optional[int] = None
        self.current_title: str = ""
        self.current_ttl_title: str = ""
        self.first_title_directive_encountered: bool = False # Added attribute
        self.skip_count: int = 0
        self.is_defining: Optional[str] = None
        self.current_definition_name: Optional[str] = None
        self.current_definition_params: List[str] = []
        self.current_definition_lines: List[str] = []
        self.block_lcs: Dict[str, int] = {'*ABS*': 0}
        self.block_order: List[str] = []
        self.error_reporter: Optional['ErrorReporter'] = error_reporter
        self.symbol_table: Optional['SymbolTable'] = symbol_table
        self.debug_mode: bool = debug_mode
        self.assembler: Optional['Assembler'] = assembler_ref
        self.deferred_force_upper_pending: bool = False
        self.lc_is_absolute_due_to_loc: bool = False
        self.pre_loc_block_name: Optional[str] = None
        self.listing_preamble_mode: bool = False
        self.first_listable_line_num_for_header: Optional[int] = None
        self.suppress_next_title_listing: bool = False
        self.eject_requested: bool = False
        self.current_remote_block_name: Optional[str] = None

    def set_pass(self, pass_num: int):
        self.pass_number = pass_num
        if self.debug_mode: print(f"DEBUG STATE: set_pass called with {pass_num}. Current line: {self.current_line_number}")
        if pass_num == 1:
            self.block_lcs = {'*ABS*': 0}
            self.block_order = []
            self.location_counter = 0
            self.position_counter = 0
            self.current_block = '*ABS*'
            self.absolute_mode = True
            self.lc_is_absolute_due_to_loc = False
            self.pre_loc_block_name = None
            self.first_title_directive_encountered = False # Reset for Pass 1

    def reset_for_pass2(self):
        if self.debug_mode: print(f">>> DEBUG LC: Reset for Pass 2. Literal Block Size = {self.symbol_table.get_literal_block_size() if self.symbol_table else 'N/A'}")
        literal_block_size = self.symbol_table.get_literal_block_size() if self.symbol_table else 0
        self.location_counter = literal_block_size
        self.position_counter = 0
        self.conditional_stack = [(True)]
        self.end_statement_processed = False
        self.current_qualifier = None
        self.skip_count = 0
        self.is_defining = None
        self.current_definition_name = None
        self.current_definition_params = []
        self.current_definition_lines = []
        self.listing_flags = {
            'B': True, 'C': True, 'D': True, 'E': True, 'F': True, 'G': True,
            'M': True, 'N': True, 'R': True, 'S': True, 'X': True
        }
        self.current_block = '*ABS*'
        self.absolute_mode = True
        self.lc_is_absolute_due_to_loc = False
        self.pre_loc_block_name = None
        self.current_base = 'D'
        self.current_code = 'D'
        self.current_remote_block_name = None
        self.first_title_directive_encountered = False # Reset for Pass 2
        self.set_pass(2)
        if self.debug_mode: print(f">>> DEBUG LC: Reset for Pass 2 (v1.66 model)\n    Literal Block Size = {literal_block_size}\n    Initial (conceptual) LC set to {self.location_counter:o}")

    def advance_lc(self, bits: int, is_data_op: bool = False) -> int:
        if bits <= 0: return 0
        lc_before = self.location_counter
        pc_before = self.position_counter
        words_advanced_this_call = 0
        if self.debug_mode: print(f">>> DEBUG LC: advance_lc({bits}) -> words_advanced_this_call={words_advanced_this_call}")
        if self.debug_mode: print(f"    State Before: AbsLC={lc_before:o}, PC={pc_before}, Block={self.current_block}, LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")
        new_total_bits = pc_before + bits
        words_advanced_this_call = new_total_bits // MAX_BITS_IN_WORD
        final_pc = new_total_bits % MAX_BITS_IN_WORD
        new_lc = lc_before + words_advanced_this_call
        self.location_counter = new_lc
        self.position_counter = final_pc
        if self.pass_number == 1:
            target_block_for_size = self.pre_loc_block_name if self.lc_is_absolute_due_to_loc and self.pre_loc_block_name else self.current_block
            if self.debug_mode: print(f"    ADVANCE_LC PRE-CHECK: self.block_lcs['{target_block_for_size}'] = {self.block_lcs.get(target_block_for_size, 0):o}")
            if not self.lc_is_absolute_due_to_loc:
                self.block_lcs[target_block_for_size] = new_lc
            elif self.pre_loc_block_name and words_advanced_this_call > 0 :
                 current_size_of_pre_loc_block = self.block_lcs.get(self.pre_loc_block_name, 0)
                 self.block_lcs[self.pre_loc_block_name] = current_size_of_pre_loc_block + words_advanced_this_call
                 if self.debug_mode: print(f"    >>> DEBUG {self.pre_loc_block_name} SIZE (advance_lc during LOC L{self.current_line_number}): Added {words_advanced_this_call} words. Old size: {current_size_of_pre_loc_block:o}, New size: {self.block_lcs[self.pre_loc_block_name]:o}.")
        if self.debug_mode:
            print(f"    State After : AbsLC={self.location_counter:o}, PC={self.position_counter}, Block={self.current_block}, LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")
            if self.pass_number == 1:
                print(f"    Block '{self.current_block}' size is now: {self.block_lcs.get(self.current_block,0):o}")
                if self.pre_loc_block_name:
                    print(f"    PreLOC Block '{self.pre_loc_block_name}' size is now: {self.block_lcs.get(self.pre_loc_block_name,0):o}")
        return words_advanced_this_call

    def force_upper(self, increment_block_size: bool = True):
        noop_bits_needed = 0
        if self.position_counter != 0:
            lc_before = self.location_counter
            pc_before = self.position_counter
            noop_bits_needed = MAX_BITS_IN_WORD - self.position_counter
            if self.debug_mode: print(f">>> DEBUG LC: force_upper (executed from PC={pc_before}, IncrBlk={increment_block_size})")
            if self.debug_mode: print(f"    State Before: AbsLC={lc_before:o}, PC={pc_before}, Block={self.current_block}, LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")
            new_lc = lc_before + 1
            self.location_counter = new_lc
            self.position_counter = 0
            if self.pass_number == 1 and increment_block_size:
                target_block_for_size = self.pre_loc_block_name if self.lc_is_absolute_due_to_loc and self.pre_loc_block_name else self.current_block
                if self.debug_mode: print(f"    FORCE_UPPER PRE-CHECK: self.block_lcs['{target_block_for_size}'] = {self.block_lcs.get(target_block_for_size, 0):o}")
                if not self.lc_is_absolute_due_to_loc:
                    self.block_lcs[target_block_for_size] = new_lc
                elif self.pre_loc_block_name:
                     current_size_of_pre_loc_block = self.block_lcs.get(self.pre_loc_block_name, 0)
                     self.block_lcs[self.pre_loc_block_name] = current_size_of_pre_loc_block + 1
                     if self.debug_mode: print(f"    >>> DEBUG {self.pre_loc_block_name} SIZE (force_upper during LOC L{self.current_line_number}, IncrBlk={increment_block_size}): Added 1 word. Old size: {current_size_of_pre_loc_block:o}, New size: {self.block_lcs[self.pre_loc_block_name]:o}.")
            if self.debug_mode:
                print(f"    State After : AbsLC={self.location_counter:o}, PC={self.position_counter}, Block={self.current_block}, LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")
                if self.pass_number == 1:
                    print(f"    Block '{self.current_block}' size is now: {self.block_lcs.get(self.current_block,0):o}")
                    if self.pre_loc_block_name:
                        print(f"    PreLOC Block '{self.pre_loc_block_name}' size is now: {self.block_lcs.get(self.pre_loc_block_name,0):o}")
        return noop_bits_needed

    def set_location_counter(self, new_lc: int, new_pc: int = 0, is_loc_directive: bool = False):
        lc_before = self.location_counter
        pc_before = self.position_counter
        if self.debug_mode: print(f">>> DEBUG LC: set_location_counter({new_lc:o}, {new_pc}, is_loc={is_loc_directive})")
        if self.debug_mode: print(f"    State Before: AbsLC={lc_before:o}, PC={pc_before}, Block={self.current_block}, LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")
        self.location_counter = new_lc
        self.position_counter = new_pc
        if self.position_counter < 0 or self.position_counter >= MAX_BITS_IN_WORD:
             if self.error_reporter: self.error_reporter.add_error(f"Invalid position counter value set: {new_pc}", self.current_line_number, code='F')
             self.position_counter = 0
        if self.pass_number == 1:
            if is_loc_directive:
                self.lc_is_absolute_due_to_loc = True
                if self.current_block != '*ABS*':
                    self.pre_loc_block_name = self.current_block
                if new_lc > self.block_lcs.get('*ABS*', -1):
                    self.block_lcs['*ABS*'] = new_lc
                    if self.debug_mode:
                        print(f"!!! DEBUG STATE P1 set_location_counter (L{self.current_line_number}, LOC): Updated block_lcs[*ABS*] to {new_lc:o}")
                        print(f"    Full block_lcs after update: {{{', '.join(f'{k}: {v:o}' for k, v in self.block_lcs.items())}}}")
            else:
                self.lc_is_absolute_due_to_loc = False
                self.pre_loc_block_name = None
                if self.location_counter > self.block_lcs.get(self.current_block, -1):
                    self.block_lcs[self.current_block] = self.location_counter
                    if self.debug_mode:
                        print(f"!!! DEBUG STATE P1 set_location_counter (L{self.current_line_number}, Not LOC): Updated block_lcs[{self.current_block}] to {self.location_counter:o}")
                        print(f"    Full block_lcs after update: {{{', '.join(f'{k}: {v:o}' for k, v in self.block_lcs.items())}}}")
        if self.debug_mode: print(f"    State After : AbsLC={self.location_counter:o}, PC={self.position_counter}, Block={self.current_block}, LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")

    def switch_block(self, new_block_name: str):
        if new_block_name == self.current_block and not self.lc_is_absolute_due_to_loc:
            return
        old_block = self.current_block
        if self.debug_mode: print(f">>> DEBUG LC: switch_block('{new_block_name}') (Pass {self.pass_number})")
        if self.debug_mode: print(f"    State Before: RelLC={self.location_counter:o}, PC={self.position_counter}, Block='{old_block}', LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")
        self.lc_is_absolute_due_to_loc = False
        self.pre_loc_block_name = None
        if self.pass_number == 1:
            if new_block_name not in self.block_lcs:
                self.block_lcs[new_block_name] = 0
            if new_block_name != '*ABS*' and new_block_name not in self.block_order:
                self.block_order.append(new_block_name)
            self.location_counter = self.block_lcs[new_block_name]
            self.position_counter = 0
            self.current_block = new_block_name
            self.absolute_mode = (new_block_name == '*ABS*')
        elif self.pass_number == 2:
            if self.assembler and hasattr(self.assembler, 'block_base_addresses'):
                block_base = self.assembler.block_base_addresses.get(new_block_name)
                if block_base is None:
                    if self.error_reporter: self.error_reporter.add_error(f"Internal: Base address for block '{new_block_name}' not found in Pass 2.", self.current_line_number, 'F')
                    block_base = 0
                self.location_counter = block_base
                self.position_counter = 0
                self.current_block = new_block_name
                self.absolute_mode = (new_block_name == '*ABS*')
            else:
                if self.error_reporter: self.error_reporter.add_error(f"Internal: Cannot access block base addresses during Pass 2 block switch.", self.current_line_number, 'F')
                self.location_counter = 0; self.position_counter = 0; self.current_block = new_block_name
        if self.debug_mode: print(f"    State After : RelLC={self.location_counter:o}, PC={self.position_counter}, Block='{self.current_block}', LOC_Abs={self.lc_is_absolute_due_to_loc}, PreLOCBlk={self.pre_loc_block_name}, DeferredPending={self.deferred_force_upper_pending}")

    def get_current_lc_for_listing(self) -> int:
        return self.location_counter

    def set_base(self, base_char: str):
        base_char_upper = base_char.upper()
        if base_char_upper in ['D', 'O', 'M', 'H']:
            if self.debug_mode: print(f"DEBUG STATE L{self.current_line_number} P{self.pass_number}: set_base called with '{base_char_upper}'. Old base: '{self.current_base}'.")
            if self.current_base != base_char_upper:
                self.current_base = base_char_upper
                if self.debug_mode: print(f"    Base changed to '{self.current_base}'.")
        else:
            if self.error_reporter: self.error_reporter.add_error(f"Invalid base specified: {base_char}", self.current_line_number, code='V')

    def set_code(self, code_char: str):
        code_char_upper = code_char.upper()
        if code_char_upper in ['D', 'A', 'I', 'E']:
            if self.debug_mode: print(f"DEBUG STATE L{self.current_line_number} P{self.pass_number}: set_code called with '{code_char_upper}'. Old code: '{self.current_code}'.")
            if self.current_code != code_char_upper:
                self.current_code = code_char_upper
                if self.debug_mode: print(f"    Code changed to '{self.current_code}'.")
        else:
            if self.error_reporter: self.error_reporter.add_error(f"Invalid code specified: {code_char}", self.current_line_number, code='V')

    def update_listing_flags(self, flags_str: str, turn_on: bool):
        flags_str_upper = flags_str.upper()
        target_flags = []
        if flags_str_upper == 'ALL': target_flags = list(self.listing_flags.keys())
        else: target_flags = [flag.strip() for flag in flags_str_upper.split(',') if flag.strip()]
        if self.debug_mode:
            action = "Enabling" if turn_on else "Disabling"
            print(f"DEBUG LISTING: {action} flags: {target_flags} on L{self.current_line_number}")
        for flag_char in target_flags:
            if flag_char in self.listing_flags:
                self.listing_flags[flag_char] = turn_on
            elif self.error_reporter:
                self.error_reporter.add_warning(f"Unknown listing flag '{flag_char}' encountered.", self.current_line_number, code='W')
        if self.debug_mode: print(f"DEBUG LISTING: Current flags: {self.listing_flags}")

# assembler_state.py v1.68
