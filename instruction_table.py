# instruction_table.py v1.11
"""
Manages the instruction set definitions for the CRASS assembler.
Loads instruction details from inst-map.txt and provides lookup methods.
Handles common MMi mnemonic patterns explicitly.
Added get_base_mnemonic method.
v1.8: Make _load_instruction_map more robust to comments in the operand_format
      field of inst-map.txt.
v1.9: - Fix AttributeError: 'InstructionTable' object has no attribute 'debug_mode'.
      - Fix NameError: name 'traceback' is not defined by adding import.
v1.10: Add is_macro_or_opdef method.
v1.11: Adopted v1.10. Confirmed type hints and 'NO' instruction handling.
       Retained is_macro_or_opdef method for now.
"""

import re
import sys
import traceback
from typing import Dict, List, Optional, Set, Any

class InstructionTable:
    """Loads and provides access to CDC 6000 instruction definitions."""
    def __init__(self, map_file: str = 'inst-map.txt'): # Added type hint for map_file
        self._instructions: Dict[str, List[Dict[str, Any]]] = {}
        self._pseudo_ops: Set[str] = set()
        self._pattern_key_map: Dict[str, str] = {}
        self._pattern_prefixes: Set[str] = {'SA', 'SB', 'SX', 'LX', 'AX', 'FX', 'RX', 'DX', 'IX', 'NX', 'ZX', 'UX', 'PX', 'MX', 'CX', 'BX'}
        self._map_file: str = map_file
        # Removed debug_mode attribute as it's not used within this class directly
        # print(f"Initializing InstructionTable from '{map_file}'...") # Keep print for now
        self._load_pseudo_ops()
        self._load_instruction_map()
        self._build_pattern_key_map()

    def _load_pseudo_ops(self):
        """Initializes the set of known pseudo-operations."""
        # This list should be kept in sync with pseudo-op-index.txt
        self._pseudo_ops = set([
            'ABS', 'BASE', 'BSS', 'BSSZ', 'B1=1', 'B7=1', 'CHAR', 'CODE', 'COL', 'COMMENT',
            'CON', 'CPOP', 'CPSYN', 'CTEXT', 'DATA', 'DECMIC', 'DIS', 'DUP', 'ECHO',
            'EJECT', 'ELSE', 'END', 'ENDD', 'ENDIF', 'ENDM', 'ENDX', 'ENTRY', 'ENTRYC',
            'EQU', 'ERR', 'ERRMI', 'ERRNG', 'ERRNZ', 'ERRPL', 'ERRZR', 'EXT', 'HERE',
            'IDENT', 'IF', 'IFC', 'IFCP', 'IFCP6', 'IFCP7', 'IFEQ', 'IFGE', 'IFGT',
            'IFLE', 'IFLT', 'IFMI', 'IFNE', 'IFPL', 'IFPP', 'IFPP6', 'IFPP7',
            'IRP', 'LCC', 'LIST', 'LIT', 'LOC', 'LOCAL', 'MACHINE', 'MACRO', 'MACROE',
            'MAX', 'MICCNT', 'MICRO', 'MIN', 'NIL', 'NOLABEL', 'NOREF', 'OCTMIC',
            'OPDEF', 'OPSYN', 'ORG', 'ORGC', 'PERIPH', 'POS', 'PPOP', 'PPU',
            'PURGDEF', 'PURGMAC', 'QUAL', 'REP', 'REPC', 'REPI', 'RMT', 'R=',
            'SEG', 'SEGMENT', 'SET', 'SKIP', 'SPACE', 'SST', 'STEXT', 'STOPDUP',
            'TITLE', 'TTL', 'USE', 'USELCM', 'VFD', 'XREF', 'XTEXT',
            '=' # Alias for EQU
        ])
        # Ensure all pseudo_ops are uppercase for consistent checking
        self._pseudo_ops = {op.upper() for op in self._pseudo_ops}
        # print(f"Debug: Loaded {len(self._pseudo_ops)} pseudo-op names.") # Keep for now

    def _load_instruction_map(self):
        """Loads instruction definitions from the inst-map.txt file."""
        try:
            with open(self._map_file, 'r') as f:
                line_num = 0
                for line in f:
                    line_num += 1
                    line = line.strip()
                    if not line or line.startswith('#'): 
                        continue

                    parts = re.split(r'\s+', line, maxsplit=3)
                    if len(parts) < 3:
                        # print(f"Warning: Skipping malformed line {line_num} in {self._map_file}: '{line}'")
                        continue

                    width_str, opcode_str, mnemonic_raw = parts[0], parts[1], parts[2]
                    
                    operand_format = ""
                    if len(parts) > 3:
                        temp_format = parts[3]
                        # Robust comment stripping from operand_format
                        comment_match_compass_1 = re.search(r'\s+\*(.*)', temp_format)
                        comment_match_compass_2 = re.search(r'\s+\.(.*)', temp_format)
                        comment_match_hash = temp_format.find('#') 

                        end_pos = len(temp_format)
                        if comment_match_compass_1:
                            end_pos = min(end_pos, comment_match_compass_1.start())
                        if comment_match_compass_2:
                            end_pos = min(end_pos, comment_match_compass_2.start())
                        if comment_match_hash != -1:
                            end_pos = min(end_pos, comment_match_hash)
                        
                        operand_format = temp_format[:end_pos].strip()

                    try:
                        width = int(width_str)
                        opcode_val = int(opcode_str, 8) 

                        mnemonic_upper = mnemonic_raw.upper()
                        instr_def: Dict[str, Any] = {
                            'width': width,
                            'opcode_oct': opcode_str, 
                            'opcode_val': opcode_val, 
                            'format': operand_format,
                            'mnemonic': mnemonic_raw, # Keep original casing for reference if needed
                            'source_line': line_num
                        }

                        if mnemonic_upper not in self._instructions:
                            self._instructions[mnemonic_upper] = []
                        self._instructions[mnemonic_upper].append(instr_def)

                    except ValueError:
                        # print(f"Warning: Skipping invalid numeric value on line {line_num} in {self._map_file}: '{line}'")
                        continue
            
            # print(f"Debug: Loaded definitions for {len(self._instructions)} unique base mnemonics from {self._map_file}.") # Keep for now

        except FileNotFoundError:
            print(f"Error: Instruction map file not found: {self._map_file}", file=sys.stderr)
            raise SystemExit(f"Error: Missing required file '{self._map_file}'")
        except Exception as e:
            print(f"Error reading instruction map file {self._map_file}: {e}", file=sys.stderr)
            traceback.print_exc() 
            raise SystemExit(f"Error reading '{self._map_file}'")

    def _build_pattern_key_map(self):
        """Builds the map from base (e.g., SA) to map key (e.g., SAI)."""
        self._pattern_key_map = {}
        for key in self._instructions.keys():
             # SAI, SBI, SXI etc. are the entries in inst-map.txt
             if len(key) >= 3 and key[-1] == 'I' and key[:-1] in self._pattern_prefixes:
                 base = key[:-1] # e.g. SA from SAI
                 self._pattern_key_map[base] = key # Store SA -> SAI
        # print(f"Debug: Built pattern key map for {len(self._pattern_key_map)} mnemonics (e.g., SA -> SAI).") # Keep for now


    def is_instruction(self, mnemonic: str) -> bool:
        if mnemonic is None:
            return False
        mnemonic_upper = mnemonic.upper()
        if mnemonic_upper in self._instructions:
            return True
        # Check for patterned mnemonics like SA1, SB2, etc.
        if len(mnemonic_upper) >= 3: # e.g. SA1 (length 3)
            base = mnemonic_upper[:-1] # e.g. SA
            digit = mnemonic_upper[-1] # e.g. 1
            if base in self._pattern_key_map and digit in '01234567':
                 # The key in _instructions is like 'SAI', not 'SA'
                 map_key_for_base = self._pattern_key_map.get(base) # Gets 'SAI' for base 'SA'
                 if map_key_for_base and map_key_for_base in self._instructions:
                     return True
        if mnemonic_upper == 'NO' and 'NO' in self._instructions: # Specific check for 'NO'
             return True
        return False

    def is_pseudo_op(self, mnemonic: str) -> bool:
        if mnemonic is None:
            return False
        return mnemonic.upper() in self._pseudo_ops

    def get_instruction_details(self, mnemonic: str) -> Optional[List[Dict[str, Any]]]:
        if mnemonic is None:
            return None
        mnemonic_upper = mnemonic.upper()
        if mnemonic_upper in self._instructions:
            return self._instructions[mnemonic_upper]
        # Handle patterned mnemonics
        if len(mnemonic_upper) >= 3:
            base = mnemonic_upper[:-1]
            digit = mnemonic_upper[-1]
            if base in self._pattern_key_map and digit in '01234567':
                map_key = self._pattern_key_map.get(base) # e.g., 'SAI'
                if map_key:
                    return self._instructions.get(map_key)
        if mnemonic_upper == 'NO': # Specific check for 'NO'
             return self._instructions.get('NO')
        return None

    def get_base_mnemonic(self, mnemonic: str) -> str:
        if mnemonic is None:
            # Decide on consistent behavior: return None, empty string, or raise error.
            # For now, returning original mnemonic if None, though this case should be rare.
            return mnemonic # type: ignore
        mnemonic_upper = mnemonic.upper()
        if len(mnemonic_upper) >= 3:
            base = mnemonic_upper[:-1]
            digit = mnemonic_upper[-1]
            if base in self._pattern_prefixes and digit in '01234567':
                return base 
        return mnemonic_upper 

    def is_macro_or_opdef(self, mnemonic: str, macro_definitions: Dict[str, Any]) -> bool:
        """Checks if the mnemonic is a defined macro or opdef."""
        if mnemonic is None:
            return False
        # Macros and OpDefs are stored with uppercase names in macro_definitions
        return mnemonic.upper() in macro_definitions

# instruction_table.py v1.11
