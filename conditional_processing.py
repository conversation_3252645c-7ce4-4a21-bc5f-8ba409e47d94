# conditional_processing.py v1.7
"""
Handles conditional assembly directives for CRASS.
v1.5: Initial version.
v1.6: Correct unpacking of return values from evaluate_expression (expected 3, not 4).
v1.7: Add missing import for 'Union' from typing module.
"""
import re
from typing import TYPE_CHECKING, Tuple, Optional, Union # Added Union

if TYPE_CHECKING:
    from crass import Assembler
    from symbol_table import SymbolTable
    from assembler_state import AssemblerState

from expression import evaluate_expression, ExpressionError
from errors import AsmException

def evaluate_condition(
    assembler: 'Assembler',
    line_num: int,
    mnemonic: str,
    operand_str: str,
    return_expr_val: bool = False
) -> Union[bool, Tuple[bool, Optional[int]]]:
    state = assembler.state
    symbol_table = assembler.symbol_table
    error_reporter = assembler.error_reporter
    debug_mode = assembler.debug_mode

    if debug_mode: print(f"DEBUG COND L{line_num}: {mnemonic} {operand_str}")

    parts = [p.strip() for p in operand_str.split(',')]
    expr1_val_for_listing: Optional[int] = None
    result: bool = False # Ensure result is always initialized

    try:
        if mnemonic == "IF":
            if len(parts) != 2: raise ExpressionError("IF requires two operands (type, value/symbol)")
            cond_type = parts[0].upper(); arg = parts[1]
            if cond_type == "SET": result = symbol_table.is_defined(arg.upper(), state.current_qualifier)
            elif cond_type == "-SET": result = not symbol_table.is_defined(arg.upper(), state.current_qualifier)
            elif cond_type in ("ABS", "-ABS", "REL", "-REL", "COM", "-COM", "EXT", "-EXT", "LCM", "-LCM", "LOC", "-LOC", "DEF", "-DEF"):
                 value, type, _ = evaluate_expression(arg, symbol_table, state, line_num, assembler)
                 if value is None and type == 'undefined': result = False
                 elif type is None: result = False
                 elif cond_type == "ABS": result = (type == 'absolute')
                 elif cond_type == "-ABS": result = (type != 'absolute')
                 elif cond_type == "REL": result = (type == 'relocatable')
                 elif cond_type == "-REL": result = (type != 'relocatable')
                 elif cond_type == "DEF": result = True
                 elif cond_type == "-DEF": result = (value is None and type == 'undefined')
                 else: error_reporter.add_warning(f"IF condition type '{cond_type}' not fully implemented", line_num, code='W'); result = False
            elif cond_type in ("REG", "-REG"):
                 is_reg = re.fullmatch(r'[ABX][0-7]', arg.upper()) is not None
                 result = is_reg if cond_type == "REG" else not is_reg
            elif cond_type in ("MIC", "-MIC"):
                 is_micro = arg.upper() in assembler.micro_definitions
                 result = is_micro if cond_type == "MIC" else not is_micro
            else: raise ExpressionError(f"Unknown IF condition type: '{cond_type}'")

        elif mnemonic in ("IFEQ", "IFNE", "IFGT", "IFGE", "IFLT", "IFLE"):
            if len(parts) != 2: raise ExpressionError(f"{mnemonic} requires two operands")
            val1, type1, _ = evaluate_expression(parts[0], symbol_table, state, line_num, assembler)
            val2, type2, _ = evaluate_expression(parts[1], symbol_table, state, line_num, assembler)
            expr1_val_for_listing = val1 if isinstance(val1, int) else None

            if val1 is None or val2 is None: result = False
            elif type1 != 'absolute' or type2 != 'absolute':
                error_reporter.add_warning(f"{mnemonic} currently only supports comparison of absolute values.", line_num, code='W'); result = False
            elif mnemonic == "IFEQ": result = (val1 == val2)
            elif mnemonic == "IFNE": result = (val1 != val2)
            elif mnemonic == "IFGT": result = (val1 > val2)
            elif mnemonic == "IFGE": result = (val1 >= val2)
            elif mnemonic == "IFLT": result = (val1 < val2)
            elif mnemonic == "IFLE": result = (val1 <= val2)
            else: result = False

        elif mnemonic == "IFPL":
            if len(parts) != 1: raise ExpressionError("IFPL requires one expression operand")
            val, type, _ = evaluate_expression(parts[0], symbol_table, state, line_num, assembler)
            expr1_val_for_listing = val if isinstance(val, int) else None
            if val is None: result = False
            elif type != 'absolute': error_reporter.add_warning(f"IFPL only supported for absolute values", line_num, code='W'); result = False
            else: result = (val >= 0)

        elif mnemonic == "IFMI":
            if len(parts) != 1: raise ExpressionError("IFMI requires one expression operand")
            val, type, _ = evaluate_expression(parts[0], symbol_table, state, line_num, assembler)
            expr1_val_for_listing = val if isinstance(val, int) else None
            if val is None: result = False
            elif type != 'absolute': error_reporter.add_warning(f"IFMI only supported for absolute values", line_num, code='W'); result = False
            else: result = (val < 0)

        elif mnemonic == "IFC":
            m = re.match(r"(\w+)\s*,(.*)", operand_str, re.IGNORECASE)
            if not m: raise ExpressionError("Invalid IFC format.")
            op = m.group(1).upper(); rest = m.group(2).strip()
            if not rest: raise ExpressionError("Missing strings for IFC")
            delim = rest[0]
            str_match = re.match(r"(?P<delim>.)(?P<s1>.*?)(?P=delim)(?P<s2>.*?)(?P=delim)$", rest)
            if not str_match or str_match.group('delim') != delim:
                parts_after_op = rest.split(delim, 3)
                if len(parts_after_op) == 4 and parts_after_op[0] == '' and parts_after_op[3] == '':
                    s1, s2 = parts_after_op[1], parts_after_op[2]
                else: raise ExpressionError(f"Invalid IFC string format or mismatched delimiters: '{rest}'")
            else: s1, s2 = str_match.group('s1'), str_match.group('s2')
            len1, len2 = len(s1), len(s2); maxlen = max(len1, len2)
            s1_padded = s1.ljust(maxlen, chr(0)); s2_padded = s2.ljust(maxlen, chr(0))
            if op == "EQ": result = (s1_padded == s2_padded)
            elif op == "NE": result = (s1_padded != s2_padded)
            elif op == "-NE": result = (s1_padded == s2_padded)
            elif op == "-EQ": result = (s1_padded != s2_padded)
            elif op == "GT": result = (s1_padded > s2_padded)
            elif op == "-LT": result = (s1_padded >= s2_padded)
            elif op == "GE": result = (s1_padded >= s2_padded)
            elif op == "-LE": result = (s1_padded > s2_padded)
            elif op == "LT": result = (s1_padded < s2_padded)
            elif op == "-GT": result = (s1_padded <= s2_padded)
            elif op == "LE": result = (s1_padded <= s2_padded)
            elif op == "-GE": result = (s1_padded < s2_padded)
            else: raise ExpressionError(f"Unknown IFC operator: '{op}'")

        elif mnemonic == "IFCP": result = True
        elif mnemonic == "IFPP": result = False
        else:
            error_reporter.add_warning(f"Conditional '{mnemonic}' not impl, assuming FALSE", line_num, code='W'); result = False

        return (result, expr1_val_for_listing) if return_expr_val else result

    except (ExpressionError, AsmException) as e:
        if not error_reporter.has_error_on_line(line_num):
            error_reporter.add_error(f"Error in {mnemonic} condition '{operand_str}': {e}", line_num, code='E')
        return (False, None) if return_expr_val else False
    except Exception as e:
        if not error_reporter.has_error_on_line(line_num):
            error_reporter.add_error(f"Unexpected error evaluating {mnemonic} condition '{operand_str}': {e}", line_num, code='F')
        traceback.print_exc(file=sys.stderr)
        return (False, None) if return_expr_val else False

# conditional_processing.py v1.7
