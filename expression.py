# expression.py v1.64
"""
Expression Evaluation for CRASS Assembler.
Handles constants, symbols, operators (+, -, *, /, ^), parentheses, relocatability,
and data item parsing for LIT/DATA.
evaluate_data_item now handles simple numbers/symbols directly.
parse_dis_operands moved here and updated to strip comments.

v1.27 Changes:
- Pass assembler object down through evaluation chain for micro lookup.
v1.28 Changes:
- Implement proper micro substitution logic.
- Integrate micro substitution into evaluate_expression.
- Remove placeholder micro handling from _parse_single_element.
v1.29 Changes:
- Fix parse_dis_operands to handle N,%MICRO% where MICRO expands to /.../.
v1.30 Changes:
- Refine parse_dis_operands logic for N,<delim>... detection after micro substitution.
v1.31 Changes:
- Further refine parse_dis_operands logic for N,<delim>... detection.
v1.32 Changes:
- Stricter logic for N,<delim>... detection in parse_dis_operands.
v1.33 Changes:
- Propagate symbol block information through expression evaluation.
- _parse_single_element now returns (value, type, block).
- evaluate_expression now returns (value, type, block).
v1.34 Changes:
- Modify _parse_single_element to calculate absolute addresses in Pass 2.
v1.62: (User inventory version)
v1.63: Standardize DISPLAY_CODE_ZERO, DISPLAY_CODE_BLANK. Add INTERNAL_CODE_ZERO, INTERNAL_CODE_BLANK.
       Rename DISPLAY_CODE_ZERO_CHAR to DISPLAY_CODE_ZERO.
v1.64: Correct call to SymbolTable.lookup in _parse_single_element (remove suppress_error kwarg).
       Handle suppress_undefined_error correctly after lookup.
"""

import re
import math
from typing import TYPE_CHECKING, List, Optional, Tuple, Dict, Any
if TYPE_CHECKING:
    from assembler_state import AssemblerState
    from symbol_table import SymbolTable
    from errors import ErrorReporter, AsmException
    from crass import Assembler


DISPLAY_CODE_MAP_ASCII_SUBSET = {
    'A': 0o01, 'B': 0o02, 'C': 0o03, 'D': 0o04, 'E': 0o05, 'F': 0o06, 'G': 0o07,
    'H': 0o10, 'I': 0o11, 'J': 0o12, 'K': 0o13, 'L': 0o14, 'M': 0o15, 'N': 0o16, 'O': 0o17,
    'P': 0o20, 'Q': 0o21, 'R': 0o22, 'S': 0o23, 'T': 0o24, 'U': 0o25, 'V': 0o26, 'W': 0o27,
    'X': 0o30, 'Y': 0o31, 'Z': 0o32, '0': 0o33, '1': 0o34, '2': 0o35, '3': 0o36, '4': 0o37,
    '5': 0o40, '6': 0o41, '7': 0o42, '8': 0o43, '9': 0o44, '+': 0o45, '-': 0o46, '*': 0o47,
    '/': 0o50, '(': 0o51, ')': 0o52, '$': 0o53, '=': 0o54, ' ': 0o55, ',': 0o56, '.': 0o57,
    '#': 0o60, '[': 0o61, ']': 0o62, ':': 0o63, '"': 0o64, '_': 0o65, '!': 0o66, '&': 0o67,
    "'": 0o70, '?': 0o71, '<': 0o72, '>': 0o73, '@': 0o74, '\\': 0o75, '^': 0o76, ';': 0o77,
}
DISPLAY_CODE_MAP = DISPLAY_CODE_MAP_ASCII_SUBSET
DISPLAY_CODE_BLANK = DISPLAY_CODE_MAP_ASCII_SUBSET[' ']
DISPLAY_CODE_ZERO = DISPLAY_CODE_MAP_ASCII_SUBSET['0']
ZERO_6BIT = 0

INTERNAL_CODE_MAP = {
    'A': 0o41, 'B': 0o42, 'C': 0o43, 'D': 0o44, 'E': 0o45, 'F': 0o46, 'G': 0o47,
    'H': 0o50, 'I': 0o51, 'J': 0o61, 'K': 0o62, 'L': 0o63, 'M': 0o64, 'N': 0o65, 'O': 0o66,
    'P': 0o67, 'Q': 0o70, 'R': 0o71, 'S': 0o21, 'T': 0o22, 'U': 0o23, 'V': 0o24, 'W': 0o25,
    'X': 0o26, 'Y': 0o27, 'Z': 0o30, '0': 0o00, '1': 0o01, '2': 0o02, '3': 0o03, '4': 0o04,
    '5': 0o05, '6': 0o06, '7': 0o07, '8': 0o10, '9': 0o11, '+': 0o60, '-': 0o40, '*': 0o54,
    '/': 0o20, '(': 0o34, ')': 0o74, '$': 0o53, '=': 0o13, ' ': 0o55, ',': 0o33, '.': 0o73,
    '#': 0o14, '[': 0o75, ']': 0o76, ':': 0o15, '"': 0o16, '_': 0o17, '!': 0o56, '&': 0o57,
    "'": 0o77, '?': 0o31, '<': 0o36, '>': 0o37, '@': 0o72, '\\': 0o32, '^': 0o12, ';': 0o52,
}
INTERNAL_CODE_BLANK = INTERNAL_CODE_MAP[' ']
INTERNAL_CODE_ZERO = INTERNAL_CODE_MAP['0']

OCTAL_TO_DISPLAY_CODE = {v: k for k, v in DISPLAY_CODE_MAP.items()}
CHAR_DATA_FMT1_REGEX = re.compile(r'([+-]?)(\d+)([CHARLZHA])(.*)')
CHAR_DATA_FMT2_REGEX = re.compile(r'([+-]?)([CHARLZHA])(.)(.*)')
CHAR_CONST_REGEX = re.compile(r'(\d+)([CHARLZHA])(.*)')
NUM_CONST_REGEX = re.compile(r'([0-9]+)([BbDdOo]?)')
REG_REGEX = re.compile(r'^[ABX][0-7]$', re.IGNORECASE)
SYM_REGEX = re.compile(r'^[A-Za-z][A-Za-z0-9]{0,7}$')
LITERAL_REGEX = re.compile(r'^=([+-]?)(.*)')
INNER_PAREN_REGEX = re.compile(r'\(([^()]*)\)')
COMMENT_START_REGEX = re.compile(r'(\.|\*).*')
MICRO_REF_FIND_REGEX = re.compile(r'%([A-Za-z][A-Za-z0-9]{0,7})%')
MAX_EVAL_DEPTH = 50
MAX_MICRO_DEPTH = 20
EQU_STAR_SYMBOLS_FOR_DEBUG = {'START', 'LOOP', 'NEXT', 'NEXT2', 'EXIT', 'BUFF'}

class ExpressionError(ValueError): pass

def substitute_micros(text: str, assembler: 'Assembler', line_num: int, depth=0) -> str:
    if depth > MAX_MICRO_DEPTH:
        if not assembler.error_reporter.has_error_on_line(line_num):
             assembler.error_reporter.add_error(f"Maximum micro substitution depth ({MAX_MICRO_DEPTH}) exceeded", line_num, code='M')
        return text
    debug_mode = assembler.debug_mode
    substituted_text = text
    match = MICRO_REF_FIND_REGEX.search(substituted_text)
    while match:
        micro_name = match.group(1).upper()
        start, end = match.span()
        if debug_mode: print(f"Debug L{line_num} MicroSub: Found '%{micro_name}%' in '{substituted_text}'")
        micro_value = assembler.micro_definitions.get(micro_name)
        if micro_value is not None:
            substituted_text = substituted_text[:start] + micro_value + substituted_text[end:]
            if debug_mode: print(f"Debug L{line_num} MicroSub: Substituted '{micro_name}' -> '{substituted_text}'")
            return substitute_micros(substituted_text, assembler, line_num, depth + 1)
        else:
            sym_entry = assembler.symbol_table.lookup(micro_name, line_num, assembler.state.current_qualifier)
            if sym_entry and sym_entry['attrs'].get('value_is_char'):
                 char_value = sym_entry['value']
                 substituted_text = substituted_text[:start] + char_value + substituted_text[end:]
                 if debug_mode: print(f"Debug L{line_num} MicroSub: Substituted char micro '{micro_name}' -> '{substituted_text}'")
                 return substitute_micros(substituted_text, assembler, line_num, depth + 1)
            else:
                 if not assembler.error_reporter.has_error_on_line(line_num):
                      assembler.error_reporter.add_error(f"Undefined micro '%{micro_name}%'", line_num, code='U')
                 match = MICRO_REF_FIND_REGEX.search(substituted_text, end)
    return substituted_text

def _apply_reloc_rules(val1, type1, op, val2, type2):
    new_val = 0; new_type = 'error'; MASK_60 = (1 << 60) - 1
    if op in ('+', '-'):
        if type1 == 'absolute' and type2 == 'absolute': new_type = 'absolute'
        elif type1 == 'absolute' and type2 == 'relocatable': new_type = 'relocatable'
        elif type1 == 'relocatable' and type2 == 'absolute': new_type = 'relocatable'
        elif type1 == 'relocatable' and type2 == 'relocatable':
            if op == '-': new_type = 'absolute'
            else: raise ExpressionError(f"Illegal op: relocatable + relocatable")
        elif type1 == 'external' or type2 == 'external':
             if type1 == 'absolute' and type2 == 'external': new_type = 'external'
             elif type1 == 'external' and type2 == 'absolute': new_type = 'external'
             elif type1 == 'external' and type2 == 'external': raise ExpressionError(f"Illegal op: external {op} external")
             else: raise ExpressionError(f"Illegal op: external {op} relocatable")
        elif type1 == 'literal_addr' or type2 == 'literal_addr':
            if type1 == 'literal_addr' and type2 == 'literal_addr':
                 if op == '-': new_type = 'absolute'
                 else: raise ExpressionError(f"Illegal op: literal_addr + literal_addr")
            elif type1 == 'literal_addr' and type2 == 'absolute': new_type = 'literal_addr'
            elif type1 == 'absolute' and type2 == 'literal_addr': new_type = 'literal_addr'
            else: raise ExpressionError(f"Unsupported op with literal address: {type1} {op} {type2}")
        else: raise ExpressionError(f"Unsupported operation: {type1} {op} {type2}")
    elif op in ('*', '/'):
        type1_eff = 'absolute' if type1 == 'literal_addr' else type1
        type2_eff = 'absolute' if type2 == 'literal_addr' else type2
        if type1_eff == 'absolute' and type2_eff == 'absolute': new_type = 'absolute'
        else: raise ExpressionError(f"Illegal op: {type1} {op} {type2} (requires absolute)")
    elif op == '^':
        type1_eff = 'absolute' if type1 == 'literal_addr' else type1
        type2_eff = 'absolute' if type2 == 'literal_addr' else type2
        if type1_eff == 'absolute' and type2_eff == 'absolute': new_type = 'absolute'
        else: raise ExpressionError(f"Illegal op: {type1} ^ {type2} (requires absolute)")
    else: raise ExpressionError(f"Internal error: Unknown operator '{op}'")
    if op == '+': new_val = val1 + val2
    elif op == '-': new_val = val1 - val2
    elif op == '*': new_val = val1 * val2
    elif op == '/': new_val = 0 if val2 == 0 else int(val1 / val2)
    elif op == '^': new_val = val1 ^ val2
    return new_val, new_type

def _parse_char_constant(n_str, type_char, char_string, assembler_state: 'AssemblerState'):
    try: n = int(n_str)
    except ValueError: raise ExpressionError(f"Invalid character count '{n_str}'")
    if n <= 0: return 0, 'absolute', None
    current_code_mode = assembler_state.current_code
    if current_code_mode == 'A': char_map, blank_code, zero_code, default_invalid_code = INTERNAL_CODE_MAP, INTERNAL_CODE_BLANK, INTERNAL_CODE_ZERO, INTERNAL_CODE_BLANK
    else: char_map, blank_code, zero_code, default_invalid_code = DISPLAY_CODE_MAP, DISPLAY_CODE_BLANK, DISPLAY_CODE_ZERO, DISPLAY_CODE_BLANK
    max_chars = 10; actual_chars_in_string = len(char_string); chars_to_process = min(n, actual_chars_in_string, max_chars)
    processed_string = char_string[:chars_to_process]; bits_list = []
    for char in processed_string:
        char_upper = char.upper()
        if char_upper in char_map: code = char_map[char_upper]
        elif char == ' ': code = blank_code
        else: code = default_invalid_code
        bits_list.append(code)
    result_value = 0; num_target_chars = min(n, max_chars)
    if type_char in ('H', 'A', 'R'): fill_code = blank_code
    elif type_char in ('C', 'L', 'Z'): fill_code = zero_code
    else: raise ExpressionError(f"Internal error: Unknown char const type '{type_char}'")
    if type_char in ('L', 'C', 'Z', 'H'):
         justified_codes = bits_list[:num_target_chars]
         while len(justified_codes) < num_target_chars: justified_codes.append(fill_code)
         temp_val = 0
         for code_val in justified_codes: temp_val = (temp_val << 6) | code_val # Renamed 'code' to 'code_val'
         result_value = temp_val << (60 - num_target_chars * 6)
    elif type_char in ('R', 'A'):
         justified_codes = bits_list[:num_target_chars]
         while len(justified_codes) < num_target_chars: justified_codes.insert(0, fill_code)
         temp_val = 0
         for code_val in justified_codes: temp_val = (temp_val << 6) | code_val # Renamed 'code' to 'code_val'
         result_value = temp_val
    else: raise ExpressionError(f"Internal error: Unknown char const type '{type_char}'")
    return result_value, 'absolute', None

def _parse_char_data_item_delimited(type_char, delimiter, rest_of_string, assembler_state: 'AssemblerState'):
    end_delim_index = rest_of_string.find(delimiter)
    if end_delim_index == -1: raise ExpressionError(f"Missing closing delimiter '{delimiter}'")
    char_string = rest_of_string[:end_delim_index]
    n = len(char_string)
    val, type, _ = _parse_char_constant(str(n), type_char, char_string, assembler_state)
    return val, type, None

def evaluate_data_item(item_str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num, assembler: 'Assembler', suppress_undefined_error=False, allow_blank_as_zero=False):
    item_str_orig = item_str = item_str.strip() if item_str else ""
    if not item_str:
        if allow_blank_as_zero:
            if assembler_state.debug_mode: print(f"DEBUG EXPR L{line_num}: evaluate_data_item: Blank item with allow_blank_as_zero=True, returning 0.")
            return 0, 'absolute', None
        raise ExpressionError("Empty data item string.")
    if assembler_state.debug_mode: print(f"DEBUG EXPR L{line_num}: evaluate_data_item received: '{item_str_orig}' (len={len(item_str_orig)}), suppress_undef={suppress_undefined_error}, allow_blank_as_zero={allow_blank_as_zero}")

    try:
        subst_item_str = substitute_micros(item_str, assembler, line_num)
        if assembler_state.debug_mode and subst_item_str != item_str:
            print(f"    evaluate_data_item after micro sub: '{subst_item_str}'")
        item_str_for_eval = subst_item_str
    except AsmException as e:
        raise ExpressionError(f"Error during micro substitution in '{item_str_orig}': {e}")

    sign = 1
    if item_str_for_eval.startswith('+'): item_str_for_eval = item_str_for_eval[1:]
    elif item_str_for_eval.startswith('-'): sign = -1; item_str_for_eval = item_str_for_eval[1:]
    item_str_for_eval = item_str_for_eval.strip()
    if not item_str_for_eval:
        if allow_blank_as_zero and sign == 1 : return 0, 'absolute', None
        raise ExpressionError("Data item contains only a sign after substitution.")

    match_char1 = CHAR_DATA_FMT1_REGEX.fullmatch(item_str_for_eval)
    if match_char1:
        _, n_str, type_char, char_string = match_char1.groups()
        val, _, _ = _parse_char_constant(n_str, type_char.upper(), char_string, assembler_state)
        if sign == -1: val = val ^ ((1<<60)-1)
        return val, 'absolute', None

    match_char2 = CHAR_DATA_FMT2_REGEX.match(item_str_for_eval)
    if match_char2:
         _, type_char, delimiter, rest = match_char2.groups()
         try:
              val, _, _ = _parse_char_data_item_delimited(type_char.upper(), delimiter, rest, assembler_state)
              if sign == -1: val = val ^ ((1<<60)-1)
              return val, 'absolute', None
         except ExpressionError: pass

    try:
        value, type, block = evaluate_expression(item_str_for_eval, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error=suppress_undefined_error)
        if sign == -1:
             if type != 'absolute': raise ExpressionError("Cannot apply unary minus to non-absolute data item")
             value = -value
             block = None
        return value, type, block
    except ExpressionError as e_expr:
        raise ExpressionError(f"Cannot parse data item '{item_str_orig}' (after micro sub: '{item_str_for_eval}') as character, numeric, or expression: {e_expr}")

def _parse_single_element(element_str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num, assembler: 'Assembler', suppress_undefined_error=False):
    element_str = element_str.strip()
    if not element_str: raise ExpressionError("Empty element in expression")
    debug_mode = getattr(assembler_state, 'debug_mode', False)
    pass_num = assembler_state.pass_number

    if element_str == '*':
        lc_val = assembler_state.location_counter
        lc_type = 'absolute' if assembler_state.lc_is_absolute_due_to_loc or assembler_state.current_block == '*ABS*' else 'relocatable'
        lc_block = None if lc_type == 'absolute' else assembler_state.current_block
        if pass_num == 2 and lc_type == 'relocatable' and lc_block and lc_block != '*ABS*':
            block_base = assembler.block_base_addresses.get(lc_block, 0)
            lc_val += block_base
            lc_type = 'absolute'
            lc_block = None
        return lc_val, lc_type, lc_block
    elif element_str == '$': return max(0, assembler_state.position_counter - 1), 'absolute', None
    elif element_str == '*P': return assembler_state.position_counter, 'absolute', None

    if REG_REGEX.match(element_str): raise ExpressionError(f"Register '{element_str}' invalid in expression")

    match_literal = LITERAL_REGEX.match(element_str)
    if match_literal:
        sign_part, content_part = match_literal.groups()
        try:
            lit_value, lit_type, _ = evaluate_data_item(sign_part + content_part, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error=suppress_undefined_error)
            if lit_type != 'absolute': raise ExpressionError(f"Literal content '{content_part}' must be absolute")
            symbol_table.add_literal(lit_value, line_num)
            lit_addr = symbol_table.lookup_literal_address(lit_value, line_num)
            if lit_addr is None:
                 if pass_num == 1: return 0, 'literal_addr', None
                 raise ExpressionError(f"Failed to find address for literal '{element_str}'")
            return lit_addr, 'literal_addr', None
        except ExpressionError as e: raise ExpressionError(f"Invalid literal '{element_str}': {e}")

    match_char = CHAR_CONST_REGEX.fullmatch(element_str)
    if match_char:
        n_str, type_char, char_string = match_char.groups()
        if not n_str.isdigit(): raise ExpressionError(f"Invalid char constant in expression: '{element_str}'")
        return _parse_char_constant(n_str, type_char.upper(), char_string, assembler_state)

    match_num = NUM_CONST_REGEX.fullmatch(element_str)
    if match_num:
        num_part, base_suffix = match_num.groups()
        base = 10; base_suffix = base_suffix.upper() if base_suffix else ''; using_default_base = False
        if base_suffix in ('B', 'O'): base = 8
        elif base_suffix == 'D': base = 10
        elif not base_suffix:
            using_default_base = True; current_base_state = assembler_state.current_base
            if current_base_state == 'O': base = 8
            elif current_base_state == 'M': base = 8 if all(c in '01234567' for c in num_part) else 10
            else: base = 10
        try:
             if base == 8 and not all(c in '01234567' for c in num_part): raise ValueError(f"contains invalid octal digits")
             value = int(num_part, base)
             return value, 'absolute', None
        except ValueError as ve: raise ExpressionError(f"Invalid numeric constant '{element_str}' for base {base}: {ve}")

    if SYM_REGEX.match(element_str):
        if debug_mode: print(f"Debug L{line_num} ExprEval _parse_single_element: SymTable ID before lookup = {id(symbol_table)}")
        # Corrected call to lookup: remove suppress_error
        sym_entry = symbol_table.lookup(element_str, line_num, assembler_state.current_qualifier)
        if sym_entry:
            value = sym_entry['value']
            sym_type = sym_entry['attrs'].get('type', 'absolute')
            sym_block = sym_entry['attrs'].get('block', None)
            if debug_mode: print(f"Debug L{line_num} ExprEval P{pass_num} _parse_single_element: Lookup for '{element_str}' -> Val={value:o}, Type={sym_type}, Block={sym_block}, EQU*={sym_entry['attrs'].get('equ_star',False)}")
            if pass_num == 2 and sym_type == 'relocatable' and sym_block and sym_block != '*ABS*':
                block_base = assembler.block_base_addresses.get(sym_block)
                if block_base is None: raise ExpressionError(f"Internal: Base address for block '{sym_block}' not found for symbol '{element_str}'.")
                abs_value = value + block_base
                return abs_value, 'absolute', None
            else:
                return value, sym_type, sym_block
        else: # Symbol not found by lookup
            prog_attrs = symbol_table.get_program_name_attributes()
            if prog_attrs and element_str.upper() == prog_attrs['name']:
                return prog_attrs['value'], prog_attrs['type'], prog_attrs.get('block', '*ABS*')
            if suppress_undefined_error: # If lookup failed and we are suppressing errors
                return None, 'undefined', None
            # If not suppressing, the error would have been raised by symbol_table.lookup() already
            # However, to be safe or if lookup's error reporting changes:
            raise ExpressionError(f"Undefined symbol '{element_str}'") # This line might be redundant if lookup always raises

    raise ExpressionError(f"Cannot parse element '{element_str}'")

def _parse_term(term_str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num, assembler: 'Assembler', suppress_undefined_error=False):
    term_str = term_str.strip()
    if not term_str: raise ExpressionError("Empty term string")
    parts = re.split(r'([*/])', term_str); parts = [p.strip() for p in parts if p.strip()]
    if not parts: raise ExpressionError(f"Cannot parse term '{term_str}'")
    try:
        current_value, current_type, current_block = _parse_single_element(parts[0], symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
        if current_value is None and current_type == 'undefined': return None, 'undefined', None
    except ExpressionError as e: raise ExpressionError(f"First term '{parts[0]}': {e}")
    i = 1
    while i < len(parts):
        op = parts[i]
        if op not in ('*', '/'): raise ExpressionError(f"Expected */ found '{op}'")
        if i + 1 >= len(parts): raise ExpressionError(f"Missing element after {op}")
        next_element_str = parts[i+1]
        try:
            next_value, next_type, next_block = _parse_single_element(next_element_str, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
            if next_value is None and next_type == 'undefined': return None, 'undefined', None
        except ExpressionError as e: raise ExpressionError(f"Term '{next_element_str}': {e}")
        try:
            current_value, current_type = _apply_reloc_rules(current_value, current_type, op, next_value, next_type)
            current_block = None
        except ExpressionError as e: raise ExpressionError(f"Term '{term_str}': {e}")
        i += 2
    return (current_value, current_type, current_block)

def _evaluate_simple_expression(expr_str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num, assembler: 'Assembler', suppress_undefined_error=False):
    expr_str_orig = expr_str; expr_str = expr_str.strip()
    if not expr_str: return (0, 'absolute', None)
    initial_sign = 1
    if expr_str.startswith('+'): expr_str = expr_str[1:].lstrip()
    elif expr_str.startswith('-'): initial_sign = -1; expr_str = expr_str[1:].lstrip()
    if not expr_str:
         if initial_sign == -1: raise ExpressionError("Expression is only '-'")
         else: return(0, 'absolute', None)
    parts_low = re.split(r'([+\-^])', expr_str); parts_low = [p.strip() for p in parts_low if p.strip()]
    if not parts_low: raise ExpressionError(f"Cannot parse '{expr_str_orig}'")
    if parts_low[0] == '-':
         if len(parts_low) < 2: raise ExpressionError(f"Invalid unary: '{expr_str_orig}'")
         initial_sign *= -1; parts_low = parts_low[1:]
         if not parts_low: raise ExpressionError(f"Invalid unary: '{expr_str_orig}'")
    elif parts_low[0] == '+':
        if len(parts_low) < 2: return (0, 'absolute', None)
        parts_low = parts_low[1:]
        if not parts_low: return (0, 'absolute', None)
    try:
        current_value, current_type, current_block = _parse_term(parts_low[0], symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
        if current_value is None and current_type == 'undefined': return None, 'undefined', None
    except ExpressionError as e: raise ExpressionError(f"First term '{parts_low[0]}': {e}")
    if initial_sign == -1:
        if current_type != 'absolute' and current_type != 'literal_addr': raise ExpressionError(f"Unary minus on non-absolute: '{parts_low[0]}'")
        current_value = -current_value
        if current_type == 'literal_addr': current_type = 'absolute'
        current_block = None
    i = 1
    while i < len(parts_low):
        op = parts_low[i]
        if op not in ('+', '-', '^'): raise ExpressionError(f"Expected + - ^ found '{op}'")
        next_term_str = "0" if i + 1 >= len(parts_low) else parts_low[i+1]
        try:
            next_value, next_type, next_block = _parse_term(next_term_str, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
            if next_value is None and next_type == 'undefined': return None, 'undefined', None
        except ExpressionError as e:
             if next_term_str == "0": raise
             raise ExpressionError(f"Term '{next_term_str}' after '{op}': {e}")
        try:
            current_value, current_type = _apply_reloc_rules(current_value, current_type, op, next_value, next_type)
            if current_type == 'relocatable':
                if current_block is None and next_block is not None: current_block = next_block
                elif current_block is not None and next_block is not None and current_block != next_block:
                     current_block = None
            else: current_block = None
        except ExpressionError as e: raise ExpressionError(f"Expr '{expr_str_orig}': {e}")
        i += 2
    return (current_value, current_type, current_block)

def evaluate_expression(expr_str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num, assembler: 'Assembler', depth=0, suppress_undefined_error=False):
    if expr_str is None: return (0, 'absolute', None)
    expr_str_orig = expr_str; expr_str = expr_str.strip()
    if not expr_str: return (0, 'absolute', None)
    if depth > MAX_EVAL_DEPTH: raise ExpressionError(f"Max recursion depth exceeded.")
    debug_mode = getattr(assembler_state, 'debug_mode', False)
    if debug_mode: print(f"Debug L{line_num} ExprEval: Input='{expr_str_orig}', SymTable ID = {id(symbol_table)}, SuppressUndef={suppress_undefined_error}")
    try:
        subst_expr_str = substitute_micros(expr_str, assembler, line_num)
        if debug_mode and subst_expr_str != expr_str: print(f"    ExprEval after micro sub='{subst_expr_str}'")
        expr_str_for_eval = subst_expr_str
    except AsmException as e: raise ExpressionError(f"Error during micro substitution in '{expr_str_orig}': {e}")
    while True:
        match = INNER_PAREN_REGEX.search(expr_str_for_eval)
        if not match: break
        sub_expr = match.group(1); start_idx, end_idx = match.span()
        try:
            sub_value, sub_type, sub_block = evaluate_expression(sub_expr, symbol_table, assembler_state, line_num, assembler, depth + 1, suppress_undefined_error)
            if sub_value is None and sub_type == 'undefined': return None, 'undefined', None
        except ExpressionError as e: raise ExpressionError(f"Sub-expr '({sub_expr})': {e}")
        if sub_type == 'literal_addr': sub_type = 'absolute'
        if sub_type != 'absolute': raise ExpressionError(f"Sub-expr '({sub_expr})' non-absolute result '{sub_type}' not supported here.")
        sub_value_str = str(sub_value); prefix = expr_str_for_eval[:start_idx]; suffix = expr_str_for_eval[end_idx:]
        space_before = " " if (prefix and prefix[-1] not in "+-*/^( ") else ""; space_after = " " if (suffix and suffix[0] not in "+-*/^), ") else ""
        expr_str_for_eval = prefix + space_before + sub_value_str + space_after + suffix; expr_str_for_eval = expr_str_for_eval.strip()
    try:
        result_value, result_type, result_block = _evaluate_simple_expression(expr_str_for_eval, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
        if result_value is None and result_type == 'undefined': return None, 'undefined', None
        return result_value, result_type, result_block
    except ExpressionError as e:
        if expr_str_for_eval != expr_str_orig: raise ExpressionError(f"Simplified '{expr_str_for_eval}' from '{expr_str_orig}': {e}")
        else: raise e

def parse_dis_operands(operand_str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num, assembler: 'Assembler'):
    operand_str_orig = operand_str; operand_str = operand_str.strip() if operand_str else ""
    if not operand_str: raise ExpressionError("DIS requires operands")
    debug_mode = getattr(assembler_state, 'debug_mode', False)
    try:
        subst_operand_str = substitute_micros(operand_str, assembler, line_num)
        if debug_mode: print(f"Debug L{line_num} DIS Parse: Orig='{operand_str_orig}', Subst='{subst_operand_str}'")
    except AsmException as e:
        raise ExpressionError(f"Error during micro substitution in DIS operand '{operand_str_orig}': {e}")
    if subst_operand_str.startswith(','):
        if len(subst_operand_str) < 3: raise ExpressionError(f"Invalid DIS format 2 (too short): '{operand_str_orig}' (after sub: '{subst_operand_str}')")
        delimiter = subst_operand_str[1]
        content_after_first_delim = subst_operand_str[2:]
        try:
            end_delim_pos = content_after_first_delim.index(delimiter)
            string_part = content_after_first_delim[:end_delim_pos]
            return {'format': 2, 'delimiter': delimiter, 'string': string_part}
        except ValueError:
            raise ExpressionError(f"Missing closing '{delimiter}' in '{operand_str_orig}' (after sub: '{subst_operand_str}')")
    else:
        comma_pos = subst_operand_str.find(',')
        is_format_2 = False
        if comma_pos > 0 and len(subst_operand_str) > comma_pos + 1:
            potential_delim = subst_operand_str[comma_pos + 1]
            if not potential_delim.isalnum() and not potential_delim.isspace() and potential_delim != '%':
                rest_after_potential_delim = subst_operand_str[comma_pos + 2:]
                if potential_delim in rest_after_potential_delim:
                    delimiter = potential_delim
                    content_part = rest_after_potential_delim
                    try:
                        end_delim_pos = content_part.index(delimiter)
                        string_part = content_part[:end_delim_pos]
                        is_format_2 = True
                        return {'format': 2, 'delimiter': delimiter, 'string': string_part}
                    except ValueError:
                        raise ExpressionError(f"Missing closing '{delimiter}' in '{operand_str_orig}' (after sub: '{subst_operand_str}')")
        if not is_format_2:
            if comma_pos <= 0:
                raise ExpressionError(f"Invalid DIS format (expected N,String or ,<delim>...<delim>): '{operand_str_orig}' (after sub: '{subst_operand_str}')")
            n_str = subst_operand_str[:comma_pos].strip()
            string_and_comment = subst_operand_str[comma_pos+1:]
            parts = re.split(r'(\.|\*)', string_and_comment, 1)
            string_part = parts[0].rstrip()
            try:
                n_val, n_type, _ = evaluate_expression(n_str, symbol_table, assembler_state, line_num, assembler)
                if n_type != 'absolute' or not isinstance(n_val, int) or n_val < 0: raise ExpressionError("n must be non-negative absolute integer")
                return {'format': 1, 'n': n_val, 'string': string_part}
            except ExpressionError as e:
                raise ExpressionError(f"Invalid n expression '{n_str}' in Format 1 DIS: {e}")

def generate_dis_words(dis_operands, error_reporter: 'ErrorReporter', line_num, assembler_state: 'AssemblerState', is_micro_name_literal: bool = False):
    generated_words = []; chars_per_word = 10; fmt = dis_operands['format']; input_string = dis_operands['string']
    current_code_mode = assembler_state.current_code
    debug_mode = getattr(assembler_state, 'debug_mode', False)
    if debug_mode: print(f"Debug L{line_num} generate_dis_words: ENTER. is_micro_name_literal={is_micro_name_literal}, assembler_state.current_code='{current_code_mode}', id(assembler_state)={id(assembler_state)}")

    char_map_to_use = None; blank_code_to_use = None; zero_code_to_use = None; default_invalid_code_to_use = None
    if current_code_mode == 'A':
        char_map_to_use, blank_code_to_use, zero_code_to_use, default_invalid_code_to_use = INTERNAL_CODE_MAP, INTERNAL_CODE_BLANK, INTERNAL_CODE_ZERO, INTERNAL_CODE_BLANK
    else:
        char_map_to_use, blank_code_to_use, zero_code_to_use, default_invalid_code_to_use = DISPLAY_CODE_MAP, DISPLAY_CODE_BLANK, DISPLAY_CODE_ZERO, DISPLAY_CODE_BLANK
    if debug_mode: print(f"Debug L{line_num} generate_dis_words: Variable 'current_code_mode' (from assembler_state) is '{current_code_mode}'")

    if fmt == 1:
        n_words = dis_operands['n']; total_chars = n_words * chars_per_word; current_word = 0; bits_in = 0
        if n_words == 0:
            if input_string: error_reporter.add_warning(f"DIS Format 1 specified N=0 but string '{input_string}' provided. String ignored.", line_num, "W")
            return []
        for i in range(total_chars):
            char = input_string[i] if i < len(input_string) else ' '; char_upper = char.upper()
            code_val = char_map_to_use.get(char_upper, blank_code_to_use)
            if code_val == blank_code_to_use and char != ' ': error_reporter.add_warning(f"Invalid char '{char}' in DIS (CODE={current_code_mode}), using blank.", line_num, "C")
            current_word = (current_word << 6) | code_val; bits_in += 6
            if bits_in == 60: generated_words.append(current_word); current_word = 0; bits_in = 0
        if bits_in != 0: error_reporter.add_error(f"Internal: DIS fmt 1 not on word boundary", line_num, "F")
    elif fmt == 2:
        chars_to_pack = list(input_string); chars_to_pack.extend([None, None]); current_word = 0; bits_in = 0
        for char_or_none in chars_to_pack:
            code_val = zero_code_to_use
            if char_or_none is not None:
                char = char_or_none; char_upper = char.upper()
                code_val = char_map_to_use.get(char_upper, blank_code_to_use if char == ' ' else zero_code_to_use)
                if code_val == zero_code_to_use and char != ' ' and char_map_to_use.get(char_upper) is None : error_reporter.add_warning(f"Invalid char '{char}' in DIS (CODE={current_code_mode}), using zero.", line_num, "C")
            current_word = (current_word << 6) | code_val; bits_in += 6
            if bits_in == 60: generated_words.append(current_word); current_word = 0; bits_in = 0
        if bits_in > 0: generated_words.append(current_word << (60 - bits_in))
    return generated_words

# expression.py v1.64
