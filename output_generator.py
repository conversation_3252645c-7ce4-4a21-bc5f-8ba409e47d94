# output_generator.py v1.256
"""
Handles the generation of the listing file and the binary output file
for the CRASS assembler. Implements parcel packing.
Fixes buffer synchronization issue when adding parcels across word boundaries.
... (previous version comments) ...
v1.254: Add PSEUDO_SYMBOL_STRING_VALUE_INDICATOR and handle in write_listing_line.
v1.255: Add ENDL_PROGRAM_LENGTH_INDICATOR and handle in write_listing_line.
v1.256: Add CONDITIONAL_EXPR_VALUE_INDICATOR and handle in write_listing_line.
"""
import sys
import math
from typing import TYPE_CHECKING, Optional, List, Tuple, Union, Dict, Any

if TYPE_CHECKING:
    from assembler_state import AssemblerState
    from symbol_table import SymbolTable
    from crass import Assembler

NOOP_15_BIT = 0o46000
LINES_PER_PAGE = 55
LC_WIDTH = 5
POS_WIDTH = 2
ERR_WIDTH = 1
OCTAL_FIELD_WIDTH = 28
PSEUDO_VALUE_WIDTH_INDICATOR = -1
EQU_STAR_LC_INDICATOR = -2
PSEUDO_SYMBOL_STRING_VALUE_INDICATOR = -3
ENDL_PROGRAM_LENGTH_INDICATOR = -4
CONDITIONAL_EXPR_VALUE_INDICATOR = -5 # New indicator

BLANK_LC_PC_OCTAL_PSEUDO_OPS = {
    "TITLE", "TTL", "LIST", "NOLIST", "ABS", "REL", "USE", "BASE", "CODE",
    "QUAL", "MACHINE", "CPU", "PPU", "CMU", "SPACE", "EJECT", "COMMENT", "ERROR",
    "FIN", "REF", "NOREF", "XREF", "SEQ", "SKIP", "UNL", "CTEXT", "ENDX",
    "RMT", "HERE", "EXT", "ENTRY",
    "MACRO", "ENDM", "OPDEF", "MICRO",
    "LOCAL", "IRP", "ENDD", "DUP", "ECHO", "PURGE", "OPSYN",
    "DECMIC", "OCTMIC", "ENDMIC",
    "B1=1", "B7=1", "CHAR", "CPOP", "CPSYN", "ENTRYC",
    "ERRMI", "ERRNG", "ERRNZ", "ERRPL", "ERRZR",
    "LCC", "NIL", "NOLABEL", "PURGDEF", "PURGMAC",
    "REP", "REPC", "REPI", "R=", "SEG", "SEGMENT",
    "SST", "STEXT", "STOPDUP", "USELCM", "POS", "MAX", "MIN", "MICCNT",
    # Conditionals themselves don't show octal values unless specified by an indicator
    "IF", "IFC", "IFCP", "IFCP6", "IPCP7", "IFGE",
    "IFGT", "IFLE", "IFLT", "IFMI", "IFNE", "IFPL", "IFPP", "IFPP6", "IFPP7",
    "IFEQ", "ELSE", "ENDIF",
    "LIT", "END", "ENDL"
}

VALUE_IN_OCTAL_PSEUDO_OPS = {"EQU", "=", "SET", "BSS", "BSSZ"}


class OutputGenerator:
    def __init__(self, listing_file_handle, binary_file_handle, assembler_ref: Optional['Assembler'] = None):
        self.listing_file = listing_file_handle
        self.binary_file = binary_file_handle
        self.buffered_words: Dict[str, List[Tuple[int, int, List[Tuple[int, int]]]]] = {}
        self.current_word_parcels: List[Tuple[int, int]] = []
        self.current_word_address: int = -1
        self.current_word_bits_filled: int = 0
        self.current_word_is_data_type: bool = False
        self.current_page_number: int = 0
        self.lines_on_page: int = LINES_PER_PAGE + 1
        self.assembler_ref: Optional['Assembler'] = assembler_ref
        self.debug_mode: bool = getattr(assembler_ref, 'debug_mode', False) if assembler_ref else False
        self.block_buffers: Dict[str, Dict[str, Any]] = {}

    def _get_block_buffer(self, block_name: str) -> Dict[str, Any]:
        if block_name not in self.block_buffers:
            self.block_buffers[block_name] = {
                'word': 0,
                'bits_filled': 0,
                'address': -1,
                'parcels_list': [],
                'is_data_type': False
            }
        return self.block_buffers[block_name]

    def get_bits_in_buffer(self, block_name: str) -> int:
        buffer = self._get_block_buffer(block_name)
        return buffer['bits_filled']

    def get_buffer_is_data_type(self, block_name: str) -> bool:
        buffer = self._get_block_buffer(block_name)
        return buffer['is_data_type']

    def _format_octal_parcel(self, value, width):
        if width <= 0: return ""
        if width == 15: num_octal_digits = 5
        elif width == 30: num_octal_digits = 10
        elif width == 60: num_octal_digits = 20
        else: num_octal_digits = math.ceil(width / 3)
        fmt_str = "{:0" + str(num_octal_digits) + "o}"
        mask = (1 << width) - 1
        masked_value = value & mask
        return fmt_str.format(masked_value)

    def _format_pseudo_op_value(self, value):
        try:
            if isinstance(value, int) and value < 0:
                octal_str = f"-{abs(value):o}"
            else:
                octal_str = f"{int(value):o}"
        except (TypeError, ValueError): octal_str = "ERROR"
        return octal_str

    def _write_header(self, state: 'AssemblerState'):
        self.current_page_number += 1
        main_title = state.current_title[:40] if state.current_title else ""
        subtitle_to_print = state.current_ttl_title[:40] if state.current_ttl_title else main_title
        
        compass_info_placeholder = "CRASS Assembler"
        if self.assembler_ref and hasattr(self.assembler_ref, 'VERSION'):
            compass_info_placeholder = f"CRASS v{self.assembler_ref.VERSION}"

        page_str = f"PAGE {self.current_page_number:>5}"
        effective_title = subtitle_to_print if subtitle_to_print else main_title
        title_padding_needed = 48 - len(effective_title)
        left_title_pad = title_padding_needed // 2
        right_title_pad = title_padding_needed - left_title_pad
        header_prefix_spaces = " " * 39
        header_line1 = f"{header_prefix_spaces}{' '*left_title_pad}{effective_title}{' '*right_title_pad} {compass_info_placeholder:<25} {page_str:>15}\n"
        
        self.listing_file.write(header_line1)
        self.listing_file.write("\n")
        self.lines_on_page = 2
        state.suppress_next_title_listing = False

    def _check_page_overflow(self, state: 'AssemblerState', lines_to_add: int = 1):
        if state.eject_requested or (self.lines_on_page + lines_to_add > LINES_PER_PAGE):
            if self.listing_file != sys.stdout:
                self.listing_file.write("\f")
            self._write_header(state)
            state.eject_requested = False

    def write_listing_line(
        self,
        line_num: int,
        lc: Optional[int],
        position_bits: Optional[int],
        generated_data: Optional[Union[List[Tuple[int, int]], int, str]],
        source_line: str,
        error_code: Optional[str] = "",
        state: Optional['AssemblerState'] = None,
        is_continuation: bool = False,
        label_on_line: Optional[str] = None,
        pseudo_op_mnemonic: Optional[str] = None,
        pass1_lc_val: Optional[int] = None
    ):
        if state is None:
            print(f"Error L{line_num}: AssemblerState not provided to write_listing_line", file=sys.stderr)
            self.listing_file.write(f"{source_line}\n")
            return

        self.debug_mode = getattr(state, 'debug_mode', False)
        self._check_page_overflow(state)

        lc_str = " " * LC_WIDTH
        pos_str = " " * POS_WIDTH
        octal_str = " " * OCTAL_FIELD_WIDTH
        err_str = (error_code if error_code else " ").ljust(ERR_WIDTH)
        source_to_print = source_line if not is_continuation else ""
        
        mnemonic_upper = pseudo_op_mnemonic.upper() if pseudo_op_mnemonic else ""
        blank_lc_pc_octal = mnemonic_upper in BLANK_LC_PC_OCTAL_PSEUDO_OPS
        if mnemonic_upper == "IDENT" or (label_on_line is None and not mnemonic_upper and not source_line.strip().startswith(('*','.')) and source_line.strip() == ""):
            blank_lc_pc_octal = True
        if mnemonic_upper == "IDENT": err_str = " " * ERR_WIDTH

        if not blank_lc_pc_octal:
            if lc is not None:
                lc_str = f"{lc:0{LC_WIDTH}o}"
            if position_bits is not None:
                parcel_pos_display = position_bits // 15
                pos_str = f"{parcel_pos_display:{POS_WIDTH}d}"

        if isinstance(generated_data, list) and generated_data:
            val, indicator = generated_data[0]
            if indicator == PSEUDO_VALUE_WIDTH_INDICATOR:
                unjustified_val_str = self._format_pseudo_op_value(val)
                octal_str = unjustified_val_str.rjust(OCTAL_FIELD_WIDTH)
                if mnemonic_upper not in {"BSS", "BSSZ"}:
                    lc_str = " " * LC_WIDTH
                pos_str = " " * POS_WIDTH
            elif indicator == EQU_STAR_LC_INDICATOR:
                lc_str = f"{val:0{LC_WIDTH}o}"
                octal_str = " " * OCTAL_FIELD_WIDTH
                pos_str = " " * POS_WIDTH
            elif indicator == PSEUDO_SYMBOL_STRING_VALUE_INDICATOR:
                str_val = str(val)[:OCTAL_FIELD_WIDTH]
                octal_str = str_val.ljust(OCTAL_FIELD_WIDTH)
                lc_str = " " * LC_WIDTH
                pos_str = " " * POS_WIDTH
            elif indicator == ENDL_PROGRAM_LENGTH_INDICATOR:
                lc_str = f"{val:0{LC_WIDTH}o}"
                octal_str = " " * OCTAL_FIELD_WIDTH
                pos_str = " " * POS_WIDTH
            elif indicator == CONDITIONAL_EXPR_VALUE_INDICATOR:
                # Value 'val' is the numeric result of the conditional expression
                unjustified_val_str = self._format_pseudo_op_value(val)
                octal_str = unjustified_val_str.rjust(OCTAL_FIELD_WIDTH)
                lc_str = " " * LC_WIDTH # Conditionals don't have an LC in this sense
                pos_str = " " * POS_WIDTH
            else: 
                octal_parts = []
                bits_formatted_this_line = 0
                for p_val, p_wid in generated_data:
                    if bits_formatted_this_line + p_wid > MAX_BITS_IN_WORD: break
                    octal_parts.append(self._format_octal_parcel(p_val, p_wid))
                    bits_formatted_this_line += p_wid
                octal_str = "".join(octal_parts).ljust(OCTAL_FIELD_WIDTH)
        elif isinstance(generated_data, int) and mnemonic_upper in VALUE_IN_OCTAL_PSEUDO_OPS:
            unjustified_val_str = self._format_pseudo_op_value(generated_data)
            octal_str = unjustified_val_str.rjust(OCTAL_FIELD_WIDTH)
            if mnemonic_upper not in {"BSS", "BSSZ"}:
                 lc_str = " " * LC_WIDTH
            pos_str = " " * POS_WIDTH
        elif isinstance(generated_data, str) and mnemonic_upper == "IDENT":
            octal_str = generated_data[:OCTAL_FIELD_WIDTH].ljust(OCTAL_FIELD_WIDTH)
            lc_str = " " * LC_WIDTH
            pos_str = " " * POS_WIDTH

        listing_line = f"{lc_str} {pos_str} {err_str} {octal_str} {source_to_print}\n"
        self.listing_file.write(listing_line)
        self.lines_on_page += 1

    def add_parcel_to_binary_word(self, address: int, value: int, width: int, is_data: bool = False, target_block_name: Optional[str] = None):
        if not self.binary_file: return
        if target_block_name is None:
            if self.assembler_ref and self.assembler_ref.state:
                target_block_name = self.assembler_ref.state.current_block
            else:
                target_block_name = "*UNKNOWN_BLOCK*"
                if self.debug_mode: print(f"Warning: target_block_name is None in add_parcel_to_binary_word, defaulting to {target_block_name}")
        buffer = self._get_block_buffer(target_block_name)
        if self.debug_mode:
            print(f"DEBUG OG AddParcel: Blk='{target_block_name}', Addr={address:o}, Val={value:o}, Width={width}, IsData={is_data}. BufAddr={buffer['address']:o}, BufBits={buffer['bits_filled']}")
        if buffer['bits_filled'] > 0 and address != buffer['address']:
            if self.debug_mode: print(f"DEBUG OG: Address changed (New Addr={address:o}, Buffer Addr={buffer['address']:o}) for block '{target_block_name}'. Flushing old buffer.")
            self.flush_binary_word(pad_with_noops=(not buffer['is_data_type']), target_block_name=target_block_name)
            buffer = self._get_block_buffer(target_block_name)
        if buffer['bits_filled'] == 0:
            buffer['address'] = address
            buffer['word'] = 0
            buffer['parcels_list'] = []
            buffer['is_data_type'] = is_data
        if buffer['bits_filled'] + width > MAX_BITS_IN_WORD:
            if self.error_reporter: self.error_reporter.add_error(f"Parcel (Width={width}) overflows current word buffer (Addr={buffer['address']:o}, Bits={buffer['bits_filled']}). Assembler should have forced upper.", self.assembler_ref.state.current_line_number if self.assembler_ref and self.assembler_ref.state else 0, code='F')
            self.flush_binary_word(pad_with_noops=(not buffer['is_data_type']), target_block_name=target_block_name)
            buffer = self._get_block_buffer(target_block_name)
            buffer['address'] = address
            buffer['word'] = 0
            buffer['bits_filled'] = 0
            buffer['parcels_list'] = []
            buffer['is_data_type'] = is_data
        shift = MAX_BITS_IN_WORD - buffer['bits_filled'] - width
        mask = (1 << width) - 1
        parcel_to_add = (value & mask) << shift
        buffer['word'] |= parcel_to_add
        buffer['bits_filled'] += width
        buffer['parcels_list'].append((value, width))
        if buffer['bits_filled'] == MAX_BITS_IN_WORD:
            self._write_buffered_word_final(target_block_name)

    def add_full_word_to_binary(self, address: int, word_value: int, is_data: bool = True, target_block_name: Optional[str] = None):
        if not self.binary_file: return
        if target_block_name is None:
            if self.assembler_ref and self.assembler_ref.state: target_block_name = self.assembler_ref.state.current_block
            else: target_block_name = "*UNKNOWN_BLOCK*"
        buffer = self._get_block_buffer(target_block_name)
        if self.debug_mode: print(f"DEBUG OG AddFullWord: Blk='{target_block_name}', Addr={address:o}, Val={word_value:020o}, IsData={is_data}. BufAddr={buffer['address']:o}, BufBits={buffer['bits_filled']}")
        if buffer['bits_filled'] > 0:
            if self.debug_mode: print(f"DEBUG OG: Adding full word to block '{target_block_name}' Addr={address:o} while buffer is active. Flushing buffer.")
            self.flush_binary_word(pad_with_noops=(not buffer['is_data_type']), target_block_name=target_block_name)
            buffer = self._get_block_buffer(target_block_name)
        buffer['address'] = address
        buffer['word'] = word_value
        buffer['bits_filled'] = MAX_BITS_IN_WORD
        buffer['parcels_list'] = [(word_value, MAX_BITS_IN_WORD)]
        buffer['is_data_type'] = is_data
        self._write_buffered_word_final(target_block_name)

    def _write_buffered_word_final(self, block_name: str):
        buffer = self._get_block_buffer(block_name)
        if self.binary_file and buffer['address'] != -1:
            if buffer['bits_filled'] != MAX_BITS_IN_WORD:
                 if self.debug_mode: print(f"Warning: Writing incomplete buffered word (Addr={buffer['address']:o}, Bits={buffer['bits_filled']}) for block '{block_name}'. Zero padding.")
                 buffer['word'] <<= (MAX_BITS_IN_WORD - buffer['bits_filled'])
            if block_name not in self.buffered_words:
                self.buffered_words[block_name] = []
            self.buffered_words[block_name].append((buffer['address'], buffer['word'], list(buffer['parcels_list'])))
            if self.debug_mode: print(f"DEBUG OG StoredWord: Blk='{block_name}', Addr={buffer['address']:o}, Value={buffer['word']:020o}")
        buffer['word'] = 0
        buffer['bits_filled'] = 0
        buffer['address'] = -1
        buffer['parcels_list'] = []

    def flush_binary_word(self, pad_with_noops: bool = False, target_block_name: Optional[str] = None):
        if target_block_name is None:
            if self.assembler_ref and self.assembler_ref.state: target_block_name = self.assembler_ref.state.current_block
            else: target_block_name = "*UNKNOWN_BLOCK*"
        buffer = self._get_block_buffer(target_block_name)
        if buffer['bits_filled'] > 0:
            if self.debug_mode: print(f"DEBUG OG Flush: Blk='{target_block_name}', Addr={buffer['address']:o}, Bits={buffer['bits_filled']}, Pad={pad_with_noops}")
            if pad_with_noops and buffer['bits_filled'] < MAX_BITS_IN_WORD:
                remaining_bits = MAX_BITS_IN_WORD - buffer['bits_filled']
                if self.debug_mode: print(f"DEBUG OG: Padding with {remaining_bits} bits of NOOPs for block '{target_block_name}'")
                while remaining_bits >= 15:
                    shift = remaining_bits - 15
                    buffer['word'] |= (NOOP_15_BIT << shift)
                    buffer['parcels_list'].append((NOOP_15_BIT, 15))
                    remaining_bits -= 15
                    buffer['bits_filled'] += 15
                if remaining_bits > 0:
                     if self.debug_mode: print(f"DEBUG OG: Padding final {remaining_bits} bits with zero for block '{target_block_name}'.")
                     buffer['word'] <<= remaining_bits
                     buffer['bits_filled'] += remaining_bits
            elif buffer['bits_filled'] < MAX_BITS_IN_WORD:
                 if self.debug_mode: print(f"Warning: Flushing incomplete word Addr={buffer['address']:o} Bits={buffer['bits_filled']} for block '{target_block_name}' without NOOP padding (zero-filling).")
                 buffer['word'] <<= (MAX_BITS_IN_WORD - buffer['bits_filled'])
            buffer['bits_filled'] = MAX_BITS_IN_WORD
            self._write_buffered_word_final(target_block_name)
        elif self.debug_mode:
            print(f"DEBUG OG Flush: Buffer for block '{target_block_name}' is empty.")

    def write_all_buffered_binary_words(self, state: 'AssemblerState'):
        if not self.binary_file: return
        if self.debug_mode: print("DEBUG OG: Writing all buffered binary words to file.")
        output_order = []
        if self.assembler_ref and self.assembler_ref.symbol_table and self.assembler_ref.symbol_table.LITERAL_BLOCK_NAME in self.buffered_words:
            output_order.append(self.assembler_ref.symbol_table.LITERAL_BLOCK_NAME)
        processed_for_output = set(output_order)
        for block_name in state.block_order:
            if block_name not in processed_for_output and block_name in self.buffered_words:
                output_order.append(block_name)
                processed_for_output.add(block_name)
        for block_name in sorted(self.buffered_words.keys()):
            if block_name not in processed_for_output:
                output_order.append(block_name)
        if self.debug_mode: print(f"DEBUG OG: Binary output order: {output_order}")
        for block_name in output_order:
            if block_name in self.buffered_words:
                sorted_words_info = sorted(self.buffered_words[block_name], key=lambda x: x[0])
                for address, word_val, _ in sorted_words_info:
                    self.binary_file.write(f"{address:05o} {word_val:020o}\n")
        self.buffered_words = {}

    def print_storage_allocation_summary(self, state: 'AssemblerState'):
        self._check_page_overflow(state, lines_to_add=7)
        self.listing_file.write("\n\n        STORAGE ALLOCATION. \n  \n")
        self.listing_file.write("                    ADDRESS   LENGTH              BINARY CONTROL CARDS. \n  \n")
        self.lines_on_page += 5
        ident_val = 0
        if self.assembler_ref and self.assembler_ref.program_name_attributes:
            ident_val = self.assembler_ref.program_name_attributes.get('value',0)
        total_length = 0
        if self.assembler_ref and hasattr(self.assembler_ref, 'total_program_length'):
            total_length = self.assembler_ref.total_program_length
        program_name_str = (self.assembler_ref.program_name_attributes['name'] if self.assembler_ref and self.assembler_ref.program_name_attributes else "").ljust(8)
        self.listing_file.write(f"                     {ident_val:<7o} {total_length:<7o}          IDENT   {program_name_str}                     . MAIN PROGRAM IDENTIF\n")
        self.lines_on_page += 1
        endl_val = total_length
        end_label_str = (self.assembler_ref.end_statement_label if self.assembler_ref and self.assembler_ref.end_statement_label else "").ljust(8)
        self.listing_file.write(f"                    {endl_val:<7o}          ENDL      END  {end_label_str}         . END OF PROGRAM, WITH LABEL \n")
        self.lines_on_page += 1
        self.listing_file.write("\n  \n                                        BLOCKS    TYPE      ADDRESS    LENGTH \n  \n")
        self.lines_on_page += 4
        literal_size = 0
        if self.assembler_ref and self.assembler_ref.symbol_table:
            literal_size = self.assembler_ref.symbol_table.get_literal_block_size()
        if literal_size > 0:
            self.listing_file.write(f"                                        LITERALS* ABSOLUTE        0        {literal_size:<4o}\n")
            self.lines_on_page +=1
        if self.assembler_ref and self.assembler_ref.block_base_addresses:
            sorted_blocks = sorted(
                [(name, base) for name, base in self.assembler_ref.block_base_addresses.items()
                 if name != '*ABS*' and name != (self.assembler_ref.symbol_table.LITERAL_BLOCK_NAME if self.assembler_ref.symbol_table else '')],
                key=lambda item: item[1]
            )
            for block_name, base_addr in sorted_blocks:
                block_len = state.block_lcs.get(block_name, 0)
                self.listing_file.write(f"                                        {block_name:<8s}  ABSOLUTE   {base_addr:>8o} {block_len:>8o}\n")
                self.lines_on_page +=1
        self.listing_file.write("\n")
        self.lines_on_page +=1

    def print_literal_pool_content(self, symbol_table: 'SymbolTable', state: 'AssemblerState'):
        if not state.listing_flags.get('X', True): return
        literal_pool = symbol_table.get_literal_pool()
        if not literal_pool: return
        self._check_page_overflow(state, lines_to_add=4 + len(literal_pool))
        self.listing_file.write("\n\n                     CONTENT OF LITERALS BLOCK.\n")
        self.lines_on_page += 3
        for lit_val in literal_pool:
            lit_addr = symbol_table.lookup_literal_address(lit_val, 0)
            if lit_addr is not None:
                octal_str = f"{lit_val:020o}"
                char_repr = ""
                first_char_val = (lit_val >> 54) & 0o77
                if 0o01 <= first_char_val <= 0o77 and first_char_val != DISPLAY_CODE_ZERO and first_char_val != DISPLAY_CODE_BLANK:
                    is_single_char_like = True
                    temp_val = lit_val
                    for _ in range(9):
                        temp_val <<= 6
                        char_code = (temp_val >> 54) & 0o77
                        if char_code != DISPLAY_CODE_BLANK and char_code != DISPLAY_CODE_ZERO:
                            is_single_char_like = False; break
                    if is_single_char_like:
                        from expression import OCTAL_TO_DISPLAY_CODE
                        if first_char_val in OCTAL_TO_DISPLAY_CODE:
                             char_repr = OCTAL_TO_DISPLAY_CODE[first_char_val]
                listing_line = f"            {lit_addr:<5o}  {octal_str}    {char_repr} \n"
                self.listing_file.write(listing_line)
                self.lines_on_page += 1
        self.listing_file.write("\n")
        self.lines_on_page +=1

    def print_final_summary(self, state: 'AssemblerState'):
        self._check_page_overflow(state, lines_to_add=5)
        total_length_oct = 0
        if self.assembler_ref and hasattr(self.assembler_ref, 'total_program_length'):
            total_length_oct = self.assembler_ref.total_program_length
        num_statements = state.current_line_number
        num_symbols = len(state.symbol_table.symbols) if state.symbol_table else 0
        self.listing_file.write("\n\n")
        self.listing_file.write(f"                             {total_length_oct:05o}B CM  STORAGE USED                {num_statements:<4d} STATEMENTS         {num_symbols:<2d} SYMBOLS \n")
        self.listing_file.write(f"                               PARALLEL CPU ASSEMBLY            0.000 SECONDS          0 REFERENCES\n")
        self.lines_on_page += 4
        if state.error_reporter:
            num_errors = len(state.error_reporter.errors)
            num_warnings = len(state.error_reporter.warnings)
            self.listing_file.write(f"\n{num_errors} ERRORS, {num_warnings} WARNINGS\n")
            self.lines_on_page +=2

    def close(self):
        if self.assembler_ref and self.assembler_ref.state:
            for block_name in list(self.block_buffers.keys()):
                self.flush_binary_word(pad_with_noops=(not self.get_buffer_is_data_type(block_name)), target_block_name=block_name)
            self.write_all_buffered_binary_words(self.assembler_ref.state)
        if self.listing_file and self.listing_file != sys.stdout:
            if self.debug_mode: print(f"Debug OG: Closing listing file: {self.listing_file.name}")
            self.listing_file.close()
        if self.binary_file:
            if self.debug_mode: print(f"Debug OG: Closing binary file: {self.binary_file.name}")
            self.binary_file.close()
        if self.debug_mode: print("Debug OG: OutputGenerator closed.")

# output_generator.py v1.256
