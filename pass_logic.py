# pass_logic.py v3.00
"""
Contains the pass processing logic for CRASS assembler.
Moved from crass.py v1.79.
... (previous version comments) ...
v2.99: Correct AttributeError: 'source_lines' to 'lines' when accessing assembler attributes.
v3.00: Add missing import for ErrorReporter in _estimate_instruction_width_pass1.
"""
import traceback
import sys
from typing import List, Optional, Tuple, Dict, Any, TYPE_CHECKING
import re

if TYPE_CHECKING:
    from crass import Assembler
    # from errors import ErrorReporter # Now imported directly below
    from symbol_table import SymbolTable
    from instruction_table import InstructionTable
    from assembler_state import AssemblerState
    from output_generator import OutputGenerator


from lexer import parse_line
from errors import AsmException, ErrorReporter # Import ErrorReporter here
from expression import ExpressionError, evaluate_expression, evaluate_data_item
from operand_parser import parse_operands, OperandParseError
from output_generator import OutputGenerator as OutputGeneratorClass
import output_generator as og_module
from assembler_state import AssemblerState as AssemblerStateClass
from assembler_state import handle_force_upper as global_handle_force_upper
from pass1_processing import process_line_pass_1
from pass2_processing import process_line_pass_2
from conditional_processing import evaluate_condition


PREAMBLE_DEFINING_DIRECTIVES = {"IDENT", "LIST", "NOLIST", "ABS", "REL", "USE", "MACHINE", "CPU", "PPU", "CMU", "BASE", "CODE", "QUAL", "SEQ", "COL"}
PREAMBLE_LISTABLE_CONTINUATION_DIRECTIVES = {"TITLE", "TTL", "COMMENT", "EJECT", "SPACE"}
PREAMBLE_TERMINATING_CODE_DATA_OPS = {"DATA", "CON", "DIS", "VFD", "BSS", "BSSZ", "LIT"}


def _estimate_instruction_width_pass1(
    assembler: 'Assembler',
    line_num: int,
    mnemonic_upper: str,
    instr_details_list: list,
    operand_str: str
) -> int:
    state = assembler.state
    symbol_table = assembler.symbol_table
    # error_reporter = assembler.error_reporter # Use local instance for temp state
    debug_mode = assembler.debug_mode

    def create_temp_state_for_parse():
        # Create a new ErrorReporter instance for the temporary state
        # to avoid polluting the main error reporter with errors from speculative parsing.
        temp_error_reporter = ErrorReporter()
        temp_state = AssemblerStateClass(error_reporter=temp_error_reporter, symbol_table=symbol_table, debug_mode=debug_mode, assembler_ref=assembler)
        temp_state.current_base = state.current_base
        temp_state.current_code = state.current_code
        temp_state.pass_number = 1
        temp_state.current_qualifier = state.current_qualifier
        temp_state.location_counter = 0 # Sizing should be independent of current LC
        temp_state.position_counter = 0
        return temp_state

    defs_15bit = [d for d in instr_details_list if d.get('width') == 15]
    defs_30bit = [d for d in instr_details_list if d.get('width') == 30]
    defs_60bit = [d for d in instr_details_list if d.get('width') == 60]

    parsed_operands_for_15bit = None
    match_15bit_success = False
    parsed_fmt_15bit = ""
    last_15bit_parse_error = None

    if defs_15bit:
        for instr_def_15 in defs_15bit:
            fmt_attempt = instr_def_15.get('format', "").upper()
            try:
                temp_state_15 = create_temp_state_for_parse()
                parsed_operands_for_15bit = parse_operands(
                    operand_str, fmt_attempt, symbol_table, temp_state_15,
                    line_num, assembler, suppress_undefined_error=True
                )
                parsed_fmt_15bit = parsed_operands_for_15bit.get('parsed_fmt', "")
                match_15bit_success = True
                if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Tentatively matched 15-bit fmt '{fmt_attempt}' (parsed as '{parsed_fmt_15bit}') for {mnemonic_upper} {operand_str}")
                break
            except (OperandParseError, ExpressionError, AsmException) as e:
                last_15bit_parse_error = e
                continue

    if match_15bit_success and defs_30bit:
        implies_K_field_for_15bit = (
            parsed_fmt_15bit == 'K' or
            parsed_fmt_15bit.endswith((',K', '+K', '-K')) or
            (parsed_fmt_15bit.startswith('-') and parsed_fmt_15bit.endswith('J') and not parsed_fmt_15bit.startswith('-X'))
        )
        is_typical_15bit_reg_format = (
            re.fullmatch(r"^[ABX]J,[ABX]K$", parsed_fmt_15bit) or
            re.fullmatch(r"^[ABX]J[+*/-][ABX]K$", parsed_fmt_15bit) or
            re.fullmatch(r"^-[ABX]K[+*/-][ABX]J$", parsed_fmt_15bit) or
            re.fullmatch(r"^[ABX][0-7]$", parsed_fmt_15bit) or
            parsed_fmt_15bit == "-XK" or
            parsed_fmt_15bit == "JK" or
            parsed_fmt_15bit == "BJ,XK" or parsed_fmt_15bit == "XJ,BK"
        )
        is_address_like_K_from_15bit_parse = implies_K_field_for_15bit and not is_typical_15bit_reg_format
        if is_address_like_K_from_15bit_parse:
            for instr_def_30 in defs_30bit:
                fmt_attempt_30 = instr_def_30.get('format', "").upper()
                try:
                    temp_state_30 = create_temp_state_for_parse()
                    parse_operands(
                        operand_str, fmt_attempt_30, symbol_table, temp_state_30,
                        line_num, assembler, suppress_undefined_error=True
                    )
                    if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Matched 30-bit fmt '{fmt_attempt_30}' for {mnemonic_upper} {operand_str} (preferred over K-like 15-bit)")
                    return 30
                except (OperandParseError, ExpressionError, AsmException):
                    continue
            if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: K-like 15-bit matched ({parsed_fmt_15bit}), but no 30-bit match. Using 15-bit for {mnemonic_upper} {operand_str}")
            return 15
        elif match_15bit_success:
             if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Using matched 15-bit (fmt: {parsed_fmt_15bit}) for {mnemonic_upper} {operand_str}")
             return 15

    if defs_30bit:
        for instr_def_30 in defs_30bit:
            fmt_attempt_30 = instr_def_30.get('format', "").upper()
            try:
                temp_state_30 = create_temp_state_for_parse()
                parse_operands(
                    operand_str, fmt_attempt_30, symbol_table, temp_state_30,
                    line_num, assembler, suppress_undefined_error=True
                )
                if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Matched 30-bit fmt '{fmt_attempt_30}' for {mnemonic_upper} {operand_str} (direct 30-bit attempt)")
                return 30
            except (OperandParseError, ExpressionError, AsmException):
                continue

    if defs_60bit:
        for instr_def_60 in defs_60bit:
            fmt_attempt_60 = instr_def_60.get('format', "").upper()
            try:
                temp_state_60 = create_temp_state_for_parse()
                parse_operands(
                    operand_str, fmt_attempt_60, symbol_table, temp_state_60,
                    line_num, assembler, suppress_undefined_error=True
                )
                if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Matched 60-bit fmt '{fmt_attempt_60}' for {mnemonic_upper} {operand_str}")
                return 60
            except (OperandParseError, ExpressionError, AsmException):
                continue

    if match_15bit_success:
        if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Reverting to 15-bit match (fmt: {parsed_fmt_15bit}) for {mnemonic_upper} {operand_str} as only viable option after all checks.")
        return 15

    if instr_details_list:
        default_w = instr_details_list[0].get('width', 15)
        if default_w not in (15,30,60): default_w = 15
        if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Fallback to width {default_w} (no format match, using first def) for {mnemonic_upper} {operand_str}. Last 15-bit error: {last_15bit_parse_error}")
        return default_w

    if debug_mode: print(f"DEBUG P1 WidthEst L{line_num}: Ultimate fallback to 15-bit (no defs found at all) for {mnemonic_upper} {operand_str}")
    return 15


def _calculate_block_base_addresses(assembler: 'Assembler') -> None:
    state = assembler.state
    symbol_table = assembler.symbol_table
    debug_mode = assembler.debug_mode

    if debug_mode:
        print("\n>>> DEBUG LC: End Pass 1 - Calculating Block Bases")
        print(f"    CALC_BASES START: state.block_lcs (id={id(state.block_lcs)}) = {{ {', '.join([f'{k}: {v:o}' for k, v in state.block_lcs.items()])} }}")

    current_base_address = 0
    assembler.block_base_addresses = {}

    literal_pool_size = 0
    if symbol_table:
        literal_pool_size = symbol_table.get_literal_block_size()
        if literal_pool_size > 0:
            if debug_mode: print(f"    Literal Pool Size = {literal_pool_size:o} words ({literal_pool_size}d)")
            assembler.block_base_addresses[symbol_table.LITERAL_BLOCK_NAME] = 0
            current_base_address += literal_pool_size

    if debug_mode:
        print(f"    Initial Base Address for Code/Data Blocks (after literals) = {current_base_address:o}")
        print(f"    Block Order from Pass 1 = {state.block_order}")
        print(f"    Block Sizes (state.block_lcs at CALC_BASES entry, id={id(state.block_lcs)}) = {{ {', '.join([f'{name}: {size:o}' for name, size in state.block_lcs.items()])} }}")

    assembler.block_base_addresses["*ABS*"] = 0
    processed_blocks_for_base_calc = {symbol_table.LITERAL_BLOCK_NAME if symbol_table else '', "*ABS*"}

    for block_name in state.block_order:
        if block_name in processed_blocks_for_base_calc:
            continue
        block_size_words = state.block_lcs.get(block_name, 0)
        assembler.block_base_addresses[block_name] = current_base_address
        if debug_mode: print(f"    Assigning Named Block '{block_name}': Base={current_base_address:o}, Size={block_size_words:o}")
        current_base_address += block_size_words
        processed_blocks_for_base_calc.add(block_name)

    for block_name in sorted(state.block_lcs.keys()):
        if block_name not in processed_blocks_for_base_calc:
            block_size_words = state.block_lcs.get(block_name, 0)
            if block_size_words > 0 :
                if assembler.error_reporter: assembler.error_reporter.add_warning(f"Block '{block_name}' was defined (size {block_size_words:o}) but not explicitly placed via USE in order; appending.", 0, 'W')
                assembler.block_base_addresses[block_name] = current_base_address
                if debug_mode: print(f"    Assigning Block '{block_name}' (Out of order/Unordered): Base={current_base_address:o}, Size={block_size_words:o}")
                current_base_address += block_size_words
            processed_blocks_for_base_calc.add(block_name)

    assembler.total_program_length = current_base_address
    assembler.endl_listing_value = assembler.total_program_length

    if debug_mode:
        print(f">>> DEBUG LC: End Pass 1 - Final Calculated Bases:")
        for name_debug in sorted(assembler.block_base_addresses.keys()):
            print(f"    Block: {name_debug:<10} Base: {assembler.block_base_addresses[name_debug]:o}")
        print(f"    Total Program Length (for ENDL value & IDENT length field): {assembler.total_program_length:o} ({assembler.total_program_length}d)")
        print("--- End Block Base Calculation ---\n")


def perform_pass(assembler: 'Assembler', pass_num: int) -> bool:
    current_state = assembler.state
    symbol_table = assembler.symbol_table
    instruction_table = assembler.instruction_table
    error_reporter = assembler.error_reporter
    output_generator = assembler.output_generator
    source_lines = assembler.lines
    macro_definitions = assembler.macro_definitions
    micro_definitions = assembler.micro_definitions
    debug_mode = assembler.debug_mode

    if debug_mode:
        print(f"\n--- Starting Pass {pass_num} ---")
        print(f"Debug CRASS: Symbol table ID before Pass {pass_num}: {id(symbol_table)}")
        print(f"Debug CRASS: AssemblerState object ID for Pass {pass_num}: {id(current_state)}")
        print(f"Debug CRASS: AssemblerState.block_lcs object ID at start of Pass {pass_num}: {id(current_state.block_lcs)}")

    first_code_generating_line_idx = 0

    if pass_num == 1:
        current_state.set_pass(1)
        if debug_mode:
            print(f"PASS_LOGIC P1 AFTER SET_PASS: LC={current_state.location_counter:o}, BlockLCS={{{', '.join(f'{k}: {v:o}' for k, v in current_state.block_lcs.items())}}}")
    elif pass_num == 2:
        if not output_generator:
            error_reporter.add_error("OutputGenerator not available for Pass 2.", code='F'); return False
        
        current_state.reset_for_pass2()
        output_generator.assembler_ref = assembler

        literal_start_address = 0
        if symbol_table:
            actual_literal_block_end = symbol_table.assign_literal_addresses(literal_start_address)
            if debug_mode: print(f"DEBUG PASS_LOGIC P2: Literal pool assigned addresses. Ends at: {actual_literal_block_end-1 if actual_literal_block_end > 0 else -1:o}")
            # Write literal pool to binary output
            # Ensure buffer for literal block is initialized if not already
            output_generator._get_block_buffer(symbol_table.LITERAL_BLOCK_NAME) # Initialize if needed
            output_generator.flush_binary_word(target_block_name=symbol_table.LITERAL_BLOCK_NAME) # Clear it
            for lit_val in symbol_table.get_literal_pool():
                lit_addr = symbol_table.lookup_literal_address(lit_val, 0)
                if lit_addr is not None:
                     output_generator.add_full_word_to_binary(lit_addr, lit_val, is_data=True, target_block_name=symbol_table.LITERAL_BLOCK_NAME)

        current_state.listing_preamble_mode = True
        if debug_mode: print(f"DEBUG P2: Entering Pass 2 Preamble Loop. listing_preamble_mode=True")

        current_state.first_listable_line_num_for_header = None
        for i_hdr_ln in range(len(source_lines)):
            parsed_hdr_ln = assembler.parsed_lines.get(i_hdr_ln + 1)
            if parsed_hdr_ln and (parsed_hdr_ln.get('opcode') or parsed_hdr_ln.get('label')):
                if not parsed_hdr_ln.get('is_comment_line', False) or \
                   (parsed_hdr_ln.get('label') and parsed_hdr_ln.get('label') not in ['*','.']):
                    current_state.first_listable_line_num_for_header = i_hdr_ln + 1
                    break
        if current_state.first_listable_line_num_for_header is None and source_lines:
            current_state.first_listable_line_num_for_header = 1

        if output_generator and current_state.listing_flags.get('G', True):
            output_generator.print_storage_allocation_summary(current_state)
            current_state.suppress_next_title_listing = True

        for i_preamble in range(len(source_lines)):
            line_num_preamble = i_preamble + 1
            current_state.current_line_number = line_num_preamble
            parsed_dict_preamble = assembler.parsed_lines.get(line_num_preamble)
            if not parsed_dict_preamble:
                parsed_dict_preamble = parse_line(source_lines[i_preamble], line_num_preamble)
                parsed_dict_preamble['original'] = source_lines[i_preamble]

            mnemonic_preamble = (parsed_dict_preamble.get('opcode') or "").upper()
            is_main_content_start = False
            if not parsed_dict_preamble.get('is_comment_line', False) and \
               (mnemonic_preamble or parsed_dict_preamble.get('label')):
                if mnemonic_preamble in PREAMBLE_TERMINATING_CODE_DATA_OPS or \
                   instruction_table.is_instruction(mnemonic_preamble) or \
                   mnemonic_preamble in {"EQU", "=", "SET", "LOC", "BSS", "BSSZ", "LIT", "DATA", "CON", "DIS", "VFD"}:
                    is_main_content_start = True
                elif parsed_dict_preamble.get('label') and not mnemonic_preamble:
                    is_main_content_start = True
                elif mnemonic_preamble and \
                     mnemonic_preamble not in PREAMBLE_DEFINING_DIRECTIVES and \
                     mnemonic_preamble not in PREAMBLE_LISTABLE_CONTINUATION_DIRECTIVES:
                     is_main_content_start = True

            if is_main_content_start:
                if debug_mode: print(f"DEBUG P2 Preamble: L{line_num_preamble} - First main content line '{parsed_dict_preamble['original']}'. Ending preamble.")
                current_state.listing_preamble_mode = False
                first_code_generating_line_idx = i_preamble
                break
            
            if output_generator:
                if debug_mode: print(f"DEBUG P2 Preamble: L{line_num_preamble} - Processing/Listing preamble line '{mnemonic_preamble}'.")
                process_line_pass_2(current_state, output_generator, assembler, line_num_preamble, parsed_dict_preamble)

            if current_state.end_statement_processed:
                first_code_generating_line_idx = i_preamble + 1
                break
        else:
             first_code_generating_line_idx = len(source_lines)

        current_state.listing_preamble_mode = False
        if debug_mode: print(f"DEBUG P2: Exiting Pass 2 Preamble Loop. Main listing starts at index {first_code_generating_line_idx}.")

    line_number_for_pass_loop = 0
    start_index_for_main_loop = first_code_generating_line_idx if pass_num == 2 else 0

    for i_loop in range(start_index_for_main_loop, len(source_lines)):
        line_content = source_lines[i_loop]
        line_number_for_pass_loop = i_loop + 1
        
        if debug_mode and pass_num == 1:
            print(f"PASS_LOGIC P1 LOOP ITERATION START L{line_number_for_pass_loop}:")
            print(f"    BEFORE state updates: current_state.location_counter={current_state.location_counter:o}")
            print(f"    BEFORE state updates: current_state.block_lcs={{{', '.join(f'{k}: {v:o}' for k, v in current_state.block_lcs.items())}}}")
            print(f"    BEFORE state updates: id(current_state)={id(current_state)}, id(current_state.block_lcs)={id(current_state.block_lcs)}")

        current_state.current_line_number = line_number_for_pass_loop
        current_state.line_start_address = current_state.get_current_lc_for_listing()
        current_state.line_start_position_bits = current_state.position_counter

        if debug_mode and pass_num == 1:
            print(f"PASS_LOGIC P1 LOOP TOP L{line_number_for_pass_loop}: LC={current_state.location_counter:o}, BlockLCS={{{', '.join(f'{k}: {v:o}' for k, v in current_state.block_lcs.items())}}}")

        if debug_mode and line_number_for_pass_loop % 20 == 0 and pass_num == 1 :
             print(f"Processing Pass {pass_num}, Line {line_number_for_pass_loop}...")
             print(f"    State: LC={current_state.location_counter:o}, PC={current_state.position_counter}, Block='{current_state.current_block}'")

        try:
            parsed_dict_main: Optional[Dict[str, Any]] = None
            if pass_num == 1:
                parsed_dict_main = parse_line(line_content, line_number_for_pass_loop)
                parsed_dict_main['original'] = line_content
                parsed_dict_main['pass1_lc'] = current_state.location_counter
                parsed_dict_main['pass1_pos_bits'] = current_state.position_counter
                assembler.parsed_lines[line_number_for_pass_loop] = parsed_dict_main
            elif pass_num == 2:
                parsed_dict_main = assembler.parsed_lines.get(line_number_for_pass_loop)
                if not parsed_dict_main:
                    if line_content.strip():
                        error_reporter.add_warning(f"Line {line_number_for_pass_loop} not found in parsed lines from Pass 1. Parsing ad-hoc.", line_number_for_pass_loop, 'W')
                        parsed_dict_main = parse_line(line_content, line_number_for_pass_loop)
                        parsed_dict_main['original'] = line_content
                    else:
                        if output_generator:
                            output_generator.write_listing_line(line_number_for_pass_loop, None, 0, None, line_content, state=current_state, label_on_line=None)
                        continue

            if parsed_dict_main is None:
                error_reporter.add_error(f"Internal error: parsed_dict_main is None for line {line_number_for_pass_loop}", line_number_for_pass_loop, 'F')
                continue

            if pass_num == 1:
                if not process_line_pass_1(current_state, symbol_table, instruction_table,
                                           error_reporter, macro_definitions, micro_definitions,
                                           assembler, line_number_for_pass_loop, parsed_dict_main):
                    pass
            elif pass_num == 2 and output_generator:
                if not process_line_pass_2(current_state, output_generator, assembler,
                                           line_number_for_pass_loop, parsed_dict_main):
                    pass

            if current_state.end_statement_processed:
                if i_loop < len(source_lines) - 1:
                    all_remaining_are_ignorable = True
                    for k_rem in range(i_loop + 1, len(source_lines)):
                        remaining_line_content = source_lines[k_rem].strip()
                        if remaining_line_content and not remaining_line_content.startswith("*"):
                            all_remaining_are_ignorable = False
                            break
                    if not all_remaining_are_ignorable:
                        error_reporter.add_warning("Source lines found after END statement.", line_number_for_pass_loop + 1, code='W')
                break

        except AsmException as e:
            err_line = e.line_num if hasattr(e, 'line_num') and e.line_num else line_number_for_pass_loop
            if not error_reporter.has_error_on_line(err_line): error_reporter.add_error(str(e), err_line, code=e.code)
            if pass_num == 2 and output_generator:
                 output_generator.write_listing_line(line_number_for_pass_loop, current_state.line_start_address, current_state.line_start_position_bits,
                                                     None, line_content, error_reporter.get_error_code_for_line(err_line), state=current_state, label_on_line=parsed_dict_main.get('label') if parsed_dict_main else None)
        except Exception as e:
            tb_str = traceback.format_exc()
            error_reporter.add_error(f"Unexpected error processing line {line_number_for_pass_loop}: {e}\n{tb_str}", line_number_for_pass_loop, code='F')
            if pass_num == 2 and output_generator:
                 output_generator.write_listing_line(line_number_for_pass_loop, current_state.line_start_address, current_state.line_start_position_bits,
                                                     None, line_content, "F*", state=current_state, label_on_line=parsed_dict_main.get('label') if parsed_dict_main else None)
            return False
        
        if debug_mode and pass_num == 1:
            print(f"PASS_LOGIC P1 LOOP BOTTOM L{line_number_for_pass_loop}: LC={current_state.location_counter:o}, BlockLCS={{{', '.join(f'{k}: {v:o}' for k, v in current_state.block_lcs.items())}}}")

    if pass_num == 1:
        if debug_mode:
            print(f">>> DEBUG LC P1: End of Pass 1 main loop. Final PC={current_state.position_counter}, Deferred={current_state.deferred_force_upper_pending}")
            print(f"    P1 END OF LOOP: state.block_lcs (id={id(current_state.block_lcs)}) = {{ {', '.join([f'{k}: {v:o}' for k, v in current_state.block_lcs.items()])} }}")

        if current_state.position_counter != 0 and not current_state.deferred_force_upper_pending:
            if debug_mode: print(f">>> DEBUG LC P1: End of Pass 1 main loop, PC={current_state.position_counter}. Forcing upper for block '{current_state.current_block}' for size calculation.")
            global_handle_force_upper(current_state, None, error_reporter, line_number_for_pass_loop if line_number_for_pass_loop > 0 else len(source_lines), increment_block_size_on_force=True)

        if debug_mode:
            print(f"    P1 BEFORE CALC_BASES: state.block_lcs (id={id(current_state.block_lcs)}) = {{ {', '.join([f'{k}: {v:o}' for k, v in current_state.block_lcs.items()])} }}")
        _calculate_block_base_addresses(assembler)
        if debug_mode:
            print("--- Symbol Table (End of Pass 1) ---")
            if symbol_table: symbol_table.dump_table(file_handle=sys.stderr, block_base_addresses=assembler.block_base_addresses)
            # Other debug dumps moved here from crass.py
            print("\n--- Macro Definitions (End of Pass 1) ---")
            if not macro_definitions: print("  (No macros defined)")
            else:
                 for name, definition in macro_definitions.items():
                      print(f"  Definition: {name} (Type: {definition.get('type','??')}, Fmt: {definition.get('definition_format', 'N/A')}, Params: {definition.get('params',[])}, Lines: {len(definition.get('body',[]))})")
            print("--- End Macro Definitions ---\n")
            print("\n--- Micro Definitions (End of Pass 1) ---")
            if not micro_definitions: print("  (No micros defined)")
            else:
                 for name, value in micro_definitions.items(): print(f"  Micro: {name} = '{value}'")
            print("--- End Micro Definitions ---\n")
            print("\n--- Remote Blocks (End of Pass 1) ---")
            if not assembler.remote_blocks: print("  (No remote blocks defined)")
            else:
                 for name, data in assembler.remote_blocks.items(): print(f"  Remote Block: {name} ({len(data.get('lines',[]))} lines)")
            print("--- End Remote Blocks ---\n")


    elif pass_num == 2:
        if output_generator:
            if output_generator.get_bits_in_buffer(current_state.current_block) > 0:
                 if debug_mode: print(f"DEBUG P2: End of Pass 2, PC={current_state.position_counter}. Flushing final binary word for block '{current_state.current_block}'.")
                 output_generator.flush_binary_word(pad_with_noops=(output_generator.get_buffer_is_data_type(current_state.current_block) is False), target_block_name=current_state.current_block)
            
            output_generator.write_all_buffered_binary_words(current_state) # Write out all blocks

            if current_state.listing_flags.get('X', True) and symbol_table:
                output_generator.print_literal_pool_content(symbol_table, current_state)
            if current_state.listing_flags.get('R', True) and symbol_table:
                if output_generator.lines_on_page > og_module.LINES_PER_PAGE - 20 : current_state.eject_requested = True
                output_generator._check_page_overflow(current_state)
                symbol_table.dump_table(file_handle=output_generator.listing_file, block_base_addresses=assembler.block_base_addresses)
                output_generator.lines_on_page = og_module.LINES_PER_PAGE 
            output_generator.print_final_summary(current_state)
            # output_generator.close() # Will be closed by crass.py
        if debug_mode:
            print("--- Symbol Table (End of Pass 2) ---")
            if symbol_table: symbol_table.dump_table(file_handle=sys.stderr, block_base_addresses=assembler.block_base_addresses)

    if debug_mode:
        print(f"--- End of Pass {pass_num} ---")
        print(f"Debug CRASS: Symbol table ID after Pass {pass_num}: {id(symbol_table)}")
        print(f"Debug CRASS: AssemblerState.block_lcs object ID at end of Pass {pass_num}: {id(current_state.block_lcs)}")

    return not error_reporter.has_errors()

# pass_logic.py v3.00
