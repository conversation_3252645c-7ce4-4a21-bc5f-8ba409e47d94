# operand_parser.py v1.41
"""
Parses operand strings based on expected instruction formats.
Handles various register/expression combinations and reduced forms.
Improved format handling and register type detection.
More robust parsing based on expected format structure.
Added specific handling for formats like Bj,Xk and Reg Op Reg.
Fixes parsing for -XK, -XK*XJ, BJ,XK reductions, and XJ*XK.
Fixes regex for matching operators to avoid consuming '*' in operand.
Fixes parsing for Xj,Bk variant when format is BJ,XK (e.g., NX4 X4,B4).
Uses specific regex matching for Reg Op Reg formats.
Refines Reg Op Reg matching logic.
Fixes validation check for Reg Op Reg formats (XJ*XK etc.).

v1.32 Changes:
- Add debug print for Reg Op Reg parsing to show j and k assignments.
v1.33 Changes:
- Add debug print before returning parsed dict in REG_OP_K_REGEX block
  to check final K value being stored.
v1.34 Changes:
- Add 'assembler' argument to functions calling evaluate_expression.
v1.35 Changes:
- Capture and store K_block from expression evaluation.
v1.36 Changes:
- No functional change. Reviewed for alignment with relative value strategy.
v1.39: (User inventory version)
v1.40: Correct unpacking of return values from evaluate_expression (expected 3, not 4).
v1.41: Add missing import for 'Any' from typing module.
"""
import re
from typing import TYPE_CHECKING, Dict, Optional, Any # Added Any

if TYPE_CHECKING:
    from assembler_state import AssemblerState
    from symbol_table import SymbolTable
    from errors import ErrorReporter
    from crass import Assembler

from expression import evaluate_expression, ExpressionError

REG_REGEX_STR = r'([ABX])([0-7])'
REG_REGEX = re.compile(REG_REGEX_STR, re.IGNORECASE)
SINGLE_REG_REGEX = re.compile(f'^{REG_REGEX_STR}$', re.IGNORECASE)
NEG_XK_REGEX = re.compile(f'^-{REG_REGEX_STR}$', re.IGNORECASE)
REG_OP_REG_REGEX = re.compile(
    f'^({REG_REGEX_STR})\\s*([+*/-])\\s*({REG_REGEX_STR})$',
    re.IGNORECASE
)
REG_COMMA_K_REGEX = re.compile(
    f'^({REG_REGEX_STR})\\s*,\\s*(.+)$',
    re.IGNORECASE
)
REG_OP_K_REGEX = re.compile(
    f'^({REG_REGEX_STR})\\s*([+-])\\s*(.+)$',
    re.IGNORECASE
)
NEG_REG_OP_REG_REGEX = re.compile(
    f'^-({REG_REGEX_STR})\\s*([+*/-])\\s*({REG_REGEX_STR})$',
    re.IGNORECASE
)
INT_CONST_REGEX = re.compile(r'^[0-9]+[BDO]?$', re.IGNORECASE)


class OperandParseError(ValueError):
    """Custom exception for operand parsing errors."""
    pass

def parse_register(reg_str):
    if reg_str is None:
        raise OperandParseError("Invalid register format: None")
    match = SINGLE_REG_REGEX.fullmatch(reg_str.strip())
    if match:
        reg_type = match.group(1).upper()
        reg_num = int(match.group(2))
        return (reg_type, reg_num)
    else:
        if re.match(r'^[ABX]\d+$', reg_str.strip(), re.IGNORECASE):
             raise OperandParseError(f"Invalid register number: '{reg_str}' (must be 0-7)")
        if reg_str.strip() == '*':
             raise OperandParseError(f"Register expected, found location counter '*'")
        raise OperandParseError(f"Invalid register format: '{reg_str}'")

def _parse_expression_operand(operand_str: str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num: int, assembler: 'Assembler', suppress_undefined_error: bool = False):
    debug_mode = getattr(assembler_state, 'debug_mode', False)
    if debug_mode: print(f"Debug L{line_num} Parser: Evaluating expression operand: '{operand_str}' (Suppress Undef: {suppress_undefined_error})")
    try:
        val, type, block = evaluate_expression(operand_str, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error=suppress_undefined_error)
        if val is None and type == 'undefined' and suppress_undefined_error:
            if debug_mode: print(f"Debug L{line_num} Parser: Evaluated '{operand_str}' to Undefined (suppressed)")
            return None, 'undefined', None
        if val is None:
            raise OperandParseError(f"Expression '{operand_str}' evaluated to None unexpectedly")
        if debug_mode: print(f"Debug L{line_num} Parser: Evaluated '{operand_str}' to Value={val} (Type: {type}, Block: {block})")
        return val, type, block
    except ExpressionError as e:
        raise OperandParseError(f"Cannot evaluate expression '{operand_str}': {e}")


def parse_operands(operand_str: str, expected_format: str, symbol_table: 'SymbolTable', assembler_state: 'AssemblerState', line_num: int, assembler: 'Assembler', suppress_undefined_error: bool = False) -> Dict[str, Any]:
    if operand_str is None: operand_str = ""
    operand_str = operand_str.strip()
    parsed: Dict[str, Any] = {}
    fmt = expected_format.upper()
    operand_str_orig = operand_str
    debug_mode = getattr(assembler_state, 'debug_mode', False)

    if debug_mode: print(f"Debug L{line_num} Parser: Input='{operand_str_orig}', ExpectedFmt='{fmt}', SuppressUndef={suppress_undefined_error}")

    if fmt == "":
        if operand_str and not operand_str.startswith('.') and not operand_str.startswith('*'):
             if not re.match(r'^\s*(\*.*|\..*)?$', operand_str):
                  raise OperandParseError(f"Expected no operands, got '{operand_str}'")
        parsed['parsed_fmt'] = ""
        return parsed
    if not operand_str:
        if 'K' in fmt:
            parsed['K'] = 0
            parsed['K_type'] = 'absolute'
            parsed['K_block'] = None
            parsed['parsed_fmt'] = 'K'
            if debug_mode: print(f"Debug L{line_num} Parser: Empty operand, assuming reduced K=0 -> {parsed}")
            return parsed
        elif fmt == "JK":
            parsed['jk'] = 0
            parsed['jk_type'] = 'absolute'
            parsed['parsed_fmt'] = 'JK'
            if debug_mode: print(f"Debug L{line_num} Parser: Empty operand, assuming reduced JK=0 -> {parsed}")
            return parsed

    match_ror = REG_OP_REG_REGEX.match(operand_str)
    if match_ror:
        r1t, r1n, op, r2t, r2n = match_ror.group(2), match_ror.group(3), match_ror.group(4), match_ror.group(6), match_ror.group(7)
        parsed['j'] = int(r1n)
        parsed['k'] = int(r2n)
        parsed['op'] = op
        parsed['parsed_fmt'] = f"{r1t.upper()}J{op}{r2t.upper()}K"
        if debug_mode: print(f"Debug L{line_num} Parser: Matched Reg Op Reg -> j={parsed['j']}, k={parsed['k']}, op='{parsed['op']}' -> {parsed}")
        return parsed

    match_nror = NEG_REG_OP_REG_REGEX.match(operand_str)
    if match_nror:
        negrt, negrn, op, r2t, r2n = match_nror.group(2), match_nror.group(3), match_nror.group(4), match_nror.group(6), match_nror.group(7)
        parsed['k'] = int(negrn)
        parsed['j'] = int(r2n)
        parsed['op'] = op
        parsed['parsed_fmt'] = f"-{negrt.upper()}K{op}{r2t.upper()}J"
        if debug_mode: print(f"Debug L{line_num} Parser: Matched -Reg Op Reg -> j={parsed['j']}, k={parsed['k']}, op='{parsed['op']}' -> {parsed}")
        return parsed

    match_rck = REG_COMMA_K_REGEX.match(operand_str)
    if match_rck:
        r1t, r1n, k_expr = match_rck.group(2), match_rck.group(3), match_rck.group(4)
        try:
            r2t, r2n = parse_register(k_expr)
            parsed['j'] = int(r1n)
            parsed['k'] = r2n
            parsed['parsed_fmt'] = f"{r1t.upper()}J,{r2t.upper()}K"
            if debug_mode: print(f"Debug L{line_num} Parser: Matched Reg, Reg -> {parsed}")
            return parsed
        except OperandParseError:
            try:
                kval, ktype, kblock = _parse_expression_operand(k_expr, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
                if kval is None and ktype == 'undefined': raise OperandParseError(f"Undefined symbol in K expression: '{k_expr}'")
                if fmt == "BI,K": parsed['i'] = int(r1n)
                else: parsed['j'] = int(r1n)
                parsed['reg_type'] = r1t.upper()
                parsed['K'] = kval
                parsed['K_type'] = ktype
                parsed['K_block'] = kblock
                parsed['parsed_fmt'] = f"{r1t.upper()}{'I' if 'i' in parsed else 'J'},K"
                if debug_mode: print(f"Debug L{line_num} Parser: Matched Reg, K -> Stored K={kval}, Block={kblock} -> {parsed}")
                return parsed
            except (OperandParseError, ExpressionError) as e:
                raise OperandParseError(f"Cannot parse '{operand_str_orig}' as {r1t.upper()}{r1n},K: {e}")

    match_rok = REG_OP_K_REGEX.match(operand_str)
    if match_rok:
        r1t, r1n, op, k_expr = match_rok.group(2), match_rok.group(3), match_rok.group(4), match_rok.group(5)
        try:
            kval, ktype, kblock = _parse_expression_operand(k_expr, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
            if kval is None and ktype == 'undefined': raise OperandParseError(f"Undefined symbol in K expression: '{k_expr}'")
            if fmt == "BI+K": parsed['i'] = int(r1n)
            else: parsed['j'] = int(r1n)
            parsed['op'] = op
            parsed['reg_type'] = r1t.upper()
            parsed['K'] = kval
            parsed['K_type'] = ktype
            parsed['K_block'] = kblock
            parsed['parsed_fmt'] = f"{r1t.upper()}{'I' if 'i' in parsed else 'J'}{op}K"
            if debug_mode: print(f"Debug L{line_num} Parser: Matched Reg Op K. Storing K={kval} ({ktype}), Block={kblock}. Returning: {parsed}")
            return parsed
        except (OperandParseError, ExpressionError) as e:
            raise OperandParseError(f"Cannot parse '{operand_str_orig}' as {r1t.upper()}{r1n}{op}K: {e}")

    match_nxk = NEG_XK_REGEX.match(operand_str)
    if match_nxk:
        r1t, r1n = match_nxk.group(1), match_nxk.group(2)
        if r1t.upper() == 'X':
            parsed['k'] = int(r1n)
            parsed['j'] = 0
            parsed['parsed_fmt'] = f"-XK"
            if debug_mode: print(f"Debug L{line_num} Parser: Matched -XK -> {parsed}")
            return parsed
        else:
            raise OperandParseError(f"Format -XK expects an X register, got '{operand_str_orig}'")

    try:
        reg_type, reg_num = parse_register(operand_str)
        parsed['parsed_fmt'] = f"{reg_type}{reg_num}"
        if fmt == "XJ*XK" or fmt == "XJ+XK" or fmt == "XJ-XK" or fmt == "XJ/XK":
            parsed['j'] = reg_num
            parsed['k'] = reg_num
            parsed['op'] = fmt[2]
            parsed['parsed_fmt'] = f"XJ"
        elif fmt == "BJ,XK" and reg_type == 'X':
            parsed['j'] = 0
            parsed['k'] = reg_num
            parsed['reg_type'] = 'B'
            parsed['parsed_fmt'] = 'XK'
        elif fmt == "XK": parsed['k'] = reg_num
        else: parsed['j'] = reg_num
        parsed['reg_type'] = reg_type
        if debug_mode: print(f"Debug L{line_num} Parser: Matched Single Reg -> {parsed}")
        return parsed
    except OperandParseError:
        try:
            is_lx_ax_mx_jk_hint = fmt == "JK" and expected_format == "JK"
            is_simple_int_for_jk = INT_CONST_REGEX.match(operand_str) is not None

            if is_lx_ax_mx_jk_hint and is_simple_int_for_jk:
                if debug_mode: print(f"Debug L{line_num} Parser: Treating '{operand_str}' as JK for LX/AX/MX.")
                kval, ktype, _ = _parse_expression_operand(operand_str, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
                if kval is None and ktype == 'undefined': raise OperandParseError(f"Undefined symbol in JK expression: '{operand_str}'")
                if ktype != 'absolute': raise OperandParseError(f"jk value '{operand_str_orig}' must be absolute for LX/AX/MX")
                parsed['jk'] = kval
                parsed['jk_type'] = ktype
                parsed['parsed_fmt'] = 'JK'
            else:
                kval, ktype, kblock = _parse_expression_operand(operand_str, symbol_table, assembler_state, line_num, assembler, suppress_undefined_error)
                if kval is None and ktype == 'undefined': raise OperandParseError(f"Undefined symbol in K expression: '{operand_str}'")
                if fmt == "JK":
                    if ktype != 'absolute': raise OperandParseError(f"jk value '{operand_str_orig}' must be absolute")
                    parsed['jk'] = kval; parsed['jk_type'] = ktype; parsed['parsed_fmt'] = 'JK'
                else:
                    parsed['K'] = kval; parsed['K_type'] = ktype; parsed['K_block'] = kblock
                    if fmt in ("AJ+K", "BJ+K", "XJ+K", "BI+K", "AJ-K", "BJ-K", "XJ-K"):
                        if fmt.startswith('BI'): parsed['i'] = 0
                        else: parsed['j'] = 0
                        parsed['op'] = fmt[2] if len(fmt) > 2 and fmt[2] in '+-' else '+'
                    elif fmt in ("BI,BJ,K", "XJ,K", "BI,K"):
                        if fmt.startswith('BI'): parsed['i'] = 0
                        if fmt != "XJ,K": parsed['j'] = 0
                    parsed['parsed_fmt'] = 'K'
            if debug_mode: print(f"Debug L{line_num} Parser: Matched Expression (K or JK) -> {parsed}")
            return parsed
        except (OperandParseError, ExpressionError) as e:
            raise OperandParseError(f"Operand '{operand_str_orig}' does not match any known structure for expected format '{fmt}': Last error: {e}")

# operand_parser.py v1.41
