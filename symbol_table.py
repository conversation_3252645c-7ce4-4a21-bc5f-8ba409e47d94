# symbol_table.py v1.48
"""
Symbol Table for CRASS Assembler
Manages symbol definitions, lookups, literals, and cross-reference table generation.

v1.47:  - Corrected `_print_xref_entry` to use the definition line number from
          the symbol's data, not its value, as the primary reference for
          the cross-reference table. This fixes the XREF output for constants.
v1.48:  - Added LITERAL_BLOCK_NAME class constant.
"""
import sys
from typing import Optional, Dict, Any, List, TYPE_CHECKING

if TYPE_CHECKING:
    from assembler_state import AssemblerState
    # from crass import Assembler

class SymbolTable:
    LITERAL_BLOCK_NAME = "*LITERALS*" # Name for the conceptual literal block

    def __init__(self, error_reporter=None, debug_mode=False):
        self.symbols: Dict[str, Dict[str, Any]] = {}
        self.literals: Dict[int, Dict[str, Any]] = {}
        self.literal_list: List[int] = [] 
        self.literal_addr_map: Dict[int, int] = {} 
        self.error_reporter = error_reporter
        self.debug_mode = debug_mode
        self.program_name_attributes: Optional[Dict[str, Any]] = None
        self.equ_star_symbols = set()
        self.current_pass_for_debug = 1 
        self.noref_symbols = set()

    def _get_qualified_name(self, name: str, current_qualifier: Optional[str]) -> str:
        name = name.upper()
        if current_qualifier is None or current_qualifier == '*':
            return name
        if '$' in name: 
             return name
        return f"{current_qualifier}${name}"

    def set_current_pass_for_debug(self, pass_num: int):
        self.current_pass_for_debug = pass_num

    def add_noref_symbol(self, name: str, current_qualifier: Optional[str]):
        qualified_name = self._get_qualified_name(name, current_qualifier)
        self.noref_symbols.add(qualified_name)
        if self.debug_mode:
            print(f"DEBUG SYMTABLE: Added '{qualified_name}' to NOREF list.")

    def define(self, name: str, value: int, line_num: int, attrs: Optional[Dict[str, Any]] = None, current_qualifier: Optional[str] = None):
        if self.debug_mode:
            print(f"!!! DEBUG SYMTABLE.DEFINE (ENTRY - v1.48): L{line_num} for '{name}':") # Updated version
            print(f"    Value = {value:o}")
            print(f"    attrs param RECEIVED (id={id(attrs)}): {attrs}")
            if attrs is not None:
                print(f"    attrs param RECEIVED ['type'] = {attrs.get('type')}")
                print(f"    attrs param RECEIVED ['block'] = {attrs.get('block')}")

        qualified_name = self._get_qualified_name(name, current_qualifier)
        current_attrs_to_use: Dict[str, Any] = attrs.copy() if attrs is not None else {}

        is_set_definition = current_attrs_to_use.get('redefinable', False)
        is_program_name = current_attrs_to_use.get('program_name', False)
        is_equ_star = current_attrs_to_use.get('equ_star', False)
        is_loc_def = current_attrs_to_use.get('defined_by_loc', False)

        if self.debug_mode:
            symbol_type_for_debug = current_attrs_to_use.get('type', 'unknown')
            val_type_desc = "Value"
            if self.current_pass_for_debug == 1:
                if symbol_type_for_debug == 'relocatable': val_type_desc = "Relative"
                elif symbol_type_for_debug == 'absolute': val_type_desc = "Absolute"
                elif symbol_type_for_debug == 'external': val_type_desc = "External"
            else: val_type_desc = "Absolute (P2)"

            print(f">>> DEBUG LC: L{line_num} Define Symbol: '{qualified_name}' (SymbolTable Internal Process)")
            print(f"    Value = {value:o} ({val_type_desc} based on attrs & pass)")
            print(f"    Attrs (current_attrs_to_use, id={id(current_attrs_to_use)}): {current_attrs_to_use}")


        if qualified_name in self.symbols:
            existing_attrs = self.symbols[qualified_name]['attrs']
            existing_line = self.symbols[qualified_name]['line_num']
            existing_value = self.symbols[qualified_name]['value']
            was_set = existing_attrs.get('redefinable', False)
            was_program_name = existing_attrs.get('program_name', False)
            was_loc_def = existing_attrs.get('defined_by_loc', False)

            if was_program_name:
                 if self.error_reporter and not self.error_reporter.has_error_on_line(line_num):
                      self.error_reporter.add_error(f"Symbol '{name}' (qualified: {qualified_name}) defined by IDENT cannot be redefined.", line_num, code='L')
                 return False
            elif was_loc_def:
                 if is_loc_def and existing_value == value:
                      self.symbols[qualified_name]['attrs'].update(current_attrs_to_use)
                      return True
                 elif self.error_reporter and not self.error_reporter.has_error_on_line(line_num):
                      self.error_reporter.add_error(f"Symbol '{name}' (qualified: {qualified_name}) defined by LOC on line {existing_line} cannot be redefined by this statement.", line_num, code='L')
                 return False
            elif not was_set:
                 if existing_value == value and \
                    existing_attrs.get('type') == current_attrs_to_use.get('type') and \
                    existing_attrs.get('block') == current_attrs_to_use.get('block'):
                      self.symbols[qualified_name]['attrs'].update(current_attrs_to_use)
                      return True
                 else:
                      if self.error_reporter and not self.error_reporter.has_error_on_line(line_num):
                           self.error_reporter.add_error(f"Symbol '{name}' (qualified: {qualified_name}) already defined on line {existing_line} (Val={existing_value:o}, Attrs={existing_attrs}) and is not redefinable by new (Val={value:o}, Attrs={current_attrs_to_use}).", line_num, code='L')
                      return False
            elif was_set and not is_set_definition:
                 if self.error_reporter and not self.error_reporter.has_error_on_line(line_num):
                      self.error_reporter.add_error(f"Symbol '{name}' (qualified: {qualified_name}) defined by SET on line {existing_line} cannot be redefined by non-SET.", line_num, code='L')
                 return False
            elif was_set and is_set_definition: 
                self.symbols[qualified_name]['value'] = value
                self.symbols[qualified_name]['line_num'] = line_num
                self.symbols[qualified_name]['attrs'] = current_attrs_to_use
                return True

        self.symbols[qualified_name] = {
            'value': value,
            'line_num': line_num,
            'attrs': current_attrs_to_use,
            'references': []
        }
        if is_program_name:
             if self.program_name_attributes is not None and self.program_name_attributes['name'] != name.upper() :
                  if self.error_reporter and not self.error_reporter.has_error_on_line(line_num):
                       self.error_reporter.add_error(f"Program name '{name}' conflicts with previous IDENT '{self.program_name_attributes['name']}'.", line_num, code='L')
                  return False
             self.program_name_attributes = {'name': name.upper(), 'value': value, 'type': current_attrs_to_use.get('type', 'absolute')}

        if is_equ_star:
            self.equ_star_symbols.add(qualified_name)

        return True

    def is_defined(self, name: str, current_qualifier: Optional[str] = None) -> bool:
        name_upper = name.upper()
        qualified_name_to_check = self._get_qualified_name(name_upper, current_qualifier)
        is_in_symbols_qualified = qualified_name_to_check in self.symbols
        is_in_symbols_unqualified = False
        if qualified_name_to_check != name_upper: 
            is_in_symbols_unqualified = name_upper in self.symbols
        result = is_in_symbols_qualified or is_in_symbols_unqualified
        if self.debug_mode and name_upper == "SYMBOL2": 
            print(f"DEBUG SYMTABLE.IS_DEFINED for '{name_upper}' (Qualifier: {current_qualifier}):")
            print(f"  Qualified name to check: '{qualified_name_to_check}'")
            print(f"  Is in self.symbols (qualified check): {is_in_symbols_qualified}")
            if qualified_name_to_check != name_upper:
                print(f"  Is in self.symbols (unqualified check for '{name_upper}'): {is_in_symbols_unqualified}")
            print(f"  Final result for is_defined: {result}")
        return result

    def lookup(self, name: str, line_num: int, current_qualifier: Optional[str] = None, suppress_undefined_error: bool = False, reference_lc: Optional[int] = None) -> Optional[Dict[str, Any]]:
        name_upper = name.upper()
        qualified_name_attempt = self._get_qualified_name(name_upper, current_qualifier)
        entry = self.symbols.get(qualified_name_attempt)
        found_unqualified = False
        if not entry and current_qualifier and current_qualifier != '*' and qualified_name_attempt != name_upper:
            entry = self.symbols.get(name_upper)
            if entry:
                found_unqualified = True
        if entry:
            if self.debug_mode:
                found_type = "qualified" if not found_unqualified else "unqualified fallback"
                print(f"!!! DEBUG SYMTABLE.LOOKUP: L{line_num} Found {found_type} '{name_upper}': Value={entry['value']:o}, Attrs={entry['attrs']}")
            if reference_lc is not None:
                if 'references' not in entry:
                    entry['references'] = []
                entry['references'].append(reference_lc)
            return entry
        if self.program_name_attributes and name_upper == self.program_name_attributes['name']:
            if reference_lc is not None:
                prog_name_entry = self.symbols.get(name_upper)
                if prog_name_entry:
                    if 'references' not in prog_name_entry:
                        prog_name_entry['references'] = []
                    prog_name_entry['references'].append(reference_lc)
            return {
                'value': self.program_name_attributes['value'],
                'line_num': 0,
                'attrs': {'type': self.program_name_attributes.get('type', 'absolute'), 'program_name': True, 'block': '*ABS*'},
                'references': []
            }
        if self.error_reporter and not suppress_undefined_error:
            if not self.error_reporter.has_error_on_line(line_num):
                self.error_reporter.add_error(f"Undefined symbol '{name}' (Qualifier: {current_qualifier})", line_num, code='U')
        return None

    def add_reference(self, name: str, qualifier: Optional[str], ref_lc: int, line_num: int):
        qualified_name = self._get_qualified_name(name, qualifier)
        entry = self.symbols.get(qualified_name)
        if entry:
            if 'references' not in entry:
                entry['references'] = []
            entry['references'].append(ref_lc)
        elif self.debug_mode:
            print(f"DEBUG SYMTABLE L{line_num}: Could not add reference for '{qualified_name}' (LC={ref_lc:o}), symbol not found.")

    def get_attributes(self, name: str, current_qualifier: Optional[str] = None) -> Optional[Dict[str, Any]]:
        entry = self.lookup(name, 0, current_qualifier, suppress_undefined_error=True)
        return entry.get('attrs') if entry else None

    def get_program_name_attributes(self):
         return self.program_name_attributes

    def add_literal(self, value: int, line_num: int, origin_info: Optional[Dict[str, Any]] = None):
        if not isinstance(value, int):
            if self.error_reporter:
                self.error_reporter.add_error(f"Invalid literal value type: {type(value)} for {value}", line_num, code='F')
            return
        if value not in self.literals:
            self.literals[value] = {'defined_line': line_num, 'address': -1, 'origin_info': origin_info}
            if value not in self.literal_list:
                 self.literal_list.append(value)
        elif self.literals[value].get('origin_info') is None and origin_info is not None:
            self.literals[value]['origin_info'] = origin_info

    def assign_literal_addresses(self, start_address: int) -> int:
        current_addr = start_address
        # Sort literals by value before assigning addresses to ensure deterministic order if multiple lines define same literal
        # However, COMPASS assigns based on first encounter. self.literal_list preserves this.
        for value in self.literal_list: # Use self.literal_list to maintain order of appearance
            if value in self.literals and self.literals[value]['address'] == -1: # Assign only if not already assigned
                self.literals[value]['address'] = current_addr
                self.literal_addr_map[value] = current_addr
                current_addr += 1
        return current_addr

    def lookup_literal_address(self, value: int, line_num: int) -> Optional[int]:
        addr = self.literal_addr_map.get(value)
        if addr is None:
            # Fallback check in self.literals, though literal_addr_map should be authoritative after assignment
            if value in self.literals:
                 addr = self.literals[value].get('address')
                 if addr == -1: addr = None # Not yet assigned
            if addr is None and self.error_reporter: # Only error if truly not found/assigned
                self.error_reporter.add_error(f"Internal: Literal value {value:o} (decimal: {value}) not found or address not assigned in literal pool. Referenced on L{line_num}.", line_num, code='F')
            return None
        return addr

    def get_literal_origin_info(self, value: int) -> Optional[Dict[str, Any]]:
        if value in self.literals:
            return self.literals[value].get('origin_info')
        return None

    def get_literal_pool(self) -> List[int]:
        return self.literal_list

    def get_literal_block_size(self) -> int:
         return len(self.literal_list)

    def dump_table(self, file_handle=sys.stdout, block_base_addresses: Optional[Dict[str, int]] = None):
        def write_line(line):
            try:
                file_handle.write(line + "\n")
            except IOError:
                print(line, file=sys.__stderr__)

        unqualified_symbols = {}
        qualified_symbols_by_qualifier: Dict[str, Dict[str, Any]] = {}

        for q_name, data in self.symbols.items():
            if self.program_name_attributes and q_name == self.program_name_attributes['name'] and data['attrs'].get('program_name'):
                continue # Skip program name IDENT symbol, it's listed elsewhere

            if '$' in q_name:
                qual, simple_name = q_name.split('$', 1)
                if qual not in qualified_symbols_by_qualifier:
                    qualified_symbols_by_qualifier[qual] = {}
                qualified_symbols_by_qualifier[qual][simple_name] = data
            else:
                unqualified_symbols[q_name] = data

        if unqualified_symbols:
            for name in sorted(unqualified_symbols.keys()):
                self._print_xref_entry(name, unqualified_symbols[name], file_handle, block_base_addresses)

        for qualifier in sorted(qualified_symbols_by_qualifier.keys()):
            write_line(f"\n\n                                                  SYMBOL QUALIFIER =  {qualifier}\n")
            for simple_name in sorted(qualified_symbols_by_qualifier[qualifier].keys()):
                data = qualified_symbols_by_qualifier[qualifier][simple_name]
                self._print_xref_entry(simple_name, data, file_handle, block_base_addresses, qualifier)

    def _print_xref_entry(self, name_to_print: str, data: Dict[str, Any], file_handle,
                            block_base_addresses: Optional[Dict[str, int]],
                            display_qualifier: Optional[str] = None):
        
        qualified_name_for_check = self._get_qualified_name(name_to_print, display_qualifier)
        if qualified_name_for_check in self.noref_symbols:
            return

        attrs = data.get('attrs', {})
        raw_value = data.get('value') # This is the relative value for relocatable symbols
        sym_type = attrs.get('type', 'absolute')
        sym_block = attrs.get('block')
        display_value = raw_value # Start with raw value
        references = data.get('references', [])
        def_line_num = data.get('line_num', 0)

        # Calculate absolute display value for relocatable symbols
        if block_base_addresses and sym_type == 'relocatable' and sym_block and sym_block != '*ABS*':
            base_addr = block_base_addresses.get(sym_block)
            if base_addr is not None and isinstance(raw_value, int):
                display_value = raw_value + base_addr
            # If base_addr is None, display_value remains raw_value (which is relative)
            # This case should ideally be flagged as an error elsewhere if a block has no base.
        
        # Format the value string
        try:
            if isinstance(display_value, int):
                value_str = f"{display_value:o}" # Always print octal for XREF value column
            else: # Should ideally not happen for defined symbols
                value_str = str(display_value if display_value is not None else 'N/A')
        except (TypeError, ValueError): # Fallback if formatting fails
            value_str = str(data.get('value', 'ERROR_FORMATTING'))

        all_refs_to_print = []
        if def_line_num > 0: # Definition line is always primary
            all_refs_to_print.append(str(def_line_num)) # As string to match good.txt ref format
        
        # Add other references (which are LCs)
        for ref_lc in references:
            if isinstance(ref_lc, int):
                all_refs_to_print.append(f"{ref_lc:o}") # Print reference LCs as octal
            else: # Should not happen
                all_refs_to_print.append(str(ref_lc))
        
        # Remove duplicates while preserving order as much as possible (definition first)
        # A simple way:
        final_refs_list = []
        seen_refs = set()
        for ref_item_str in all_refs_to_print:
            if ref_item_str not in seen_refs:
                final_refs_list.append(ref_item_str)
                seen_refs.add(ref_item_str)
        
        # Sort the non-definition references numerically if they are octal numbers
        # This is tricky because def_line_num is decimal.
        # For now, just sort all as strings after def_line_num.
        if len(final_refs_list) > 1:
            # Sort references after the first (definition line)
            # To sort octal numbers correctly, they need to be converted or padded.
            # For simplicity, string sort is used, which is mostly fine for LCs.
            refs_to_sort = final_refs_list[1:]
            # Attempt numeric sort for octal values if possible
            try:
                sorted_other_refs = sorted(refs_to_sort, key=lambda x: int(x, 8) if isinstance(x, str) and all(c in "01234567" for c in x) else float('inf'))
                final_refs_list = [final_refs_list[0]] + sorted_other_refs
            except ValueError: # Fallback to string sort if conversion fails
                final_refs_list = [final_refs_list[0]] + sorted(refs_to_sort)


        line_buf = f"        {name_to_print:<12} {value_str:<24}"
        
        if not final_refs_list:
            file_handle.write(line_buf.rstrip() + "\n")
            return

        MAX_LINE_WIDTH_XREF = 100 # Max width for XREF lines
        first_ref_on_line = True
        for i, ref_str_to_print in enumerate(final_refs_list):
            if first_ref_on_line:
                line_buf += ref_str_to_print
                first_ref_on_line = False
            else:
                next_part = ", " + ref_str_to_print
                if len(line_buf) + len(next_part) > MAX_LINE_WIDTH_XREF:
                    file_handle.write(line_buf + "\n")
                    line_buf = " " * 45  # Indent for continuation lines
                    line_buf += ref_str_to_print
                else:
                    line_buf += next_part
        
        file_handle.write(line_buf + "\n")


    def lookup_symbol_value(self, name: str, line_num: int, current_qualifier: Optional[str] = None) -> Optional[int]:
        entry = self.lookup(name, line_num, current_qualifier, suppress_undefined_error=True)
        return entry['value'] if entry and isinstance(entry.get('value'), int) else None

    def get_symbol_type(self, name: str, line_num: int, current_qualifier: Optional[str] = None) -> Optional[str]:
         entry = self.lookup(name, line_num, current_qualifier, suppress_undefined_error=True)
         if entry:
              return entry['attrs'].get('type', 'absolute')
         return None

    def mark_entry_point(self, name: str, line_num: int):
         # This functionality might involve setting a flag on the symbol's attributes
         # or adding it to a separate list of entry points.
         # For now, it's a placeholder.
         if self.debug_mode:
             print(f"DEBUG SYMTABLE L{line_num}: Symbol '{name}' marked as ENTRY point (placeholder).")
         pass

    def update_symbol_value(self, name: str, value: int, line_num: int, symbol_type: str, current_qualifier: Optional[str] = None):
         # This would be used if Pass 2 needed to change a symbol's value (e.g. for forward refs resolved late)
         # Not typically needed if Pass 1 defines all symbols correctly.
         qualified_name = self._get_qualified_name(name, current_qualifier)
         if qualified_name in self.symbols:
              if self.symbols[qualified_name]['value'] != value or \
                 self.symbols[qualified_name]['attrs'].get('type') != symbol_type:
                   self.symbols[qualified_name]['value'] = value
                   self.symbols[qualified_name]['attrs']['type'] = symbol_type
                   if self.debug_mode:
                       print(f"DEBUG SYMTABLE L{line_num}: Updated symbol '{qualified_name}' to Value={value:o}, Type={symbol_type}")
         else:
              if self.error_reporter:
                   self.error_reporter.add_error(f"Internal: Attempt to update undefined symbol '{name}' (Qualified: {qualified_name}) in Pass 2", line_num, code='F')

    def get_all_symbols(self) -> Dict[str, Dict[str, Any]]:
        return self.symbols

# symbol_table.py v1.48
